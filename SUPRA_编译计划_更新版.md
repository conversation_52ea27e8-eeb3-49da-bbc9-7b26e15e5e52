# SUPRA 完整编译计划 (更新版)

## 项目概述
- **项目名称**: SUPRA (Software Defined Ultrasound Processing for Real-Time Applications)
- **工作目录**: e:\project\supra
- **编译器**: Microsoft Visual C++ 2019 (MSVC 2019) ⚠️ **必须使用MSVC，不能使用MinGW**
- **构建系统**: CMake (直接使用cmake构建，不使用Visual Studio IDE)
- **Qt版本**: 5.15.2 (路径: D:\Qt\5.15.2\msvc2019_64)
- **CMake路径**: D:\Qt\Tools\CMake_64

## ⚠️ 关于MinGW编译的重要说明

**不建议使用MinGW编译SUPRA项目，原因如下：**

1. **CUDA兼容性问题**: NVIDIA CUDA只支持MSVC编译器，不支持MinGW
2. **TBB库依赖**: 代码中硬编码了MSVC特定的库路径 (`vc14`)
3. **Windows API依赖**: 使用了MSVC特定的系统库 (`ws2_32.lib`, `wsock32.lib`)
4. **第三方库兼容性**: Qt、OpenIGTLink、ITK等库的Windows版本都是MSVC编译的
5. **ABI兼容性**: 混合使用不同编译器会导致二进制接口不兼容

**建议继续使用MSVC 2019进行编译。**

---

## 总体目标
1. 启用所有可用的功能和支持选项
2. 按依赖库分别管理到thirdparty目录
3. 自动化CMake配置，无需手动指定依赖路径
4. 确保编译过程完整且成功

---

## 阶段1：环境准备和依赖分析

### 1.1 创建目录结构
- [ ] 创建 `external/` 目录用于存放第三方库源码
- [ ] 创建 `thirdparty/` 目录用于存放编译后的依赖库
- [ ] 为每个依赖库创建独立的子目录结构：
  - [ ] `thirdparty/oneTBB/{include,lib,bin}/`
  - [ ] `thirdparty/OpenIGTLink/{include,lib,bin}/`
  - [ ] `thirdparty/ITK/{include,lib,bin}/`
  - [ ] `thirdparty/cpprestsdk/{include,lib,bin}/`
  - [ ] `thirdparty/boost/{include,lib,bin}/`
  - [ ] `thirdparty/libtorch/{include,lib,bin}/`

### 1.2 分析所有可选功能
需要启用的SUPRA选项：
- [ ] SUPRA_PROFILING=ON (性能分析)
- [ ] SUPRA_CUDA=ON (CUDA支持) ⚠️ **需要MSVC编译器**
- [ ] SUPRA_CUDA_PORTABLE=ON (便携式CUDA)
- [ ] SUPRA_TORCH=ON (PyTorch支持)
- [ ] SUPRA_BUILD_DOC=ON (文档构建)
- [ ] SUPRA_INTERFACE_GRAPHIC=ON (图形界面)
- [ ] SUPRA_INTERFACE_GRAPHIC_CMD=ON (图形界面控制台输出)
- [ ] SUPRA_INTERFACE_COMMANDLINE=ON (命令行界面)
- [ ] SUPRA_INTERFACE_EXECUTOR=ON (执行器)
- [ ] SUPRA_INTERFACE_ROS=ON (ROS接口)
- [ ] SUPRA_INTERFACE_REST=ON (REST接口)
- [ ] SUPRA_BEAMFORMER=ON (软件波束成形器)
- [ ] SUPRA_DEVICE_ULTRASOUND_SIM=ON (超声模拟器)
- [ ] SUPRA_DEVICE_TRACKING_SIM=ON (跟踪模拟器)
- [ ] SUPRA_DEVICE_TRACKING_IGTL=ON (OpenIGTLink跟踪输入)
- [ ] SUPRA_DEVICE_IGTL_OUTPUT=ON (OpenIGTLink输出)
- [ ] SUPRA_DEVICE_ITK_FILE_OUT=ON (ITK文件输出)
- [ ] SUPRA_DEVICE_ROS_IMAGE_OUT=ON (ROS图像输出)
- [ ] SUPRA_DEVICE_ROS_EDEN2020=ON (EDEN2020 ROS输出)
- [ ] SUPRA_DEVICE_TRACKING_ROS=ON (ROS跟踪输入)

---

## 阶段2：依赖库下载和编译

### 2.1 核心依赖（必需）

#### TBB (Intel Threading Building Blocks)
- [ ] 下载: `git clone https://github.com/oneapi-src/oneTBB.git external/oneTBB`
- [ ] 配置CMake构建 (使用MSVC 2019):
  ```bash
  cd external/oneTBB
  mkdir build && cd build
  cmake -G "Visual Studio 16 2019" -A x64 -DCMAKE_INSTALL_PREFIX=../../../thirdparty/oneTBB ..
  ```
- [ ] 使用CMake编译和安装:
  ```bash
  cmake --build . --config Release --parallel
  cmake --build . --config Debug --parallel
  cmake --install . --config Release
  cmake --install . --config Debug
  ```

### 2.2 图形界面依赖

#### Qt 5.15.2
- [ ] 验证Qt安装路径: D:\Qt\5.15.2\msvc2019_64
- [ ] 配置Qt环境变量
- [ ] 测试Qt模块可用性

### 2.3 医学图像处理依赖

#### OpenIGTLink
- [ ] 下载: `git clone https://github.com/openigtlink/OpenIGTLink.git external/OpenIGTLink`
- [ ] 配置CMake构建 (启用BUILD_SHARED_LIBS，使用MSVC 2019):
  ```bash
  cd external/OpenIGTLink
  mkdir build && cd build
  cmake -G "Visual Studio 16 2019" -A x64 -DBUILD_SHARED_LIBS=ON -DCMAKE_INSTALL_PREFIX=../../../thirdparty/OpenIGTLink ..
  ```
- [ ] 使用CMake编译和安装:
  ```bash
  cmake --build . --config Release --parallel
  cmake --build . --config Debug --parallel
  cmake --install . --config Release
  cmake --install . --config Debug
  ```

#### ITK (Insight Toolkit)
- [ ] 下载: `git clone https://github.com/InsightSoftwareConsortium/ITK.git external/ITK`
- [ ] 配置ITK模块 (启用ITKImageIO, ITKCommon等，使用MSVC 2019):
  ```bash
  cd external/ITK
  mkdir build && cd build
  cmake -G "Visual Studio 16 2019" -A x64 -DCMAKE_INSTALL_PREFIX=../../../thirdparty/ITK -DBUILD_SHARED_LIBS=ON ..
  ```
- [ ] 使用CMake编译和安装 (ITK编译时间较长):
  ```bash
  cmake --build . --config Release --parallel
  cmake --install . --config Release
  ```

### 2.4 REST接口依赖

#### Microsoft C++ REST SDK
- [ ] 下载: `git clone https://github.com/Microsoft/cpprestsdk.git external/cpprestsdk`
- [ ] 配置vcpkg依赖 (如需要)
- [ ] 使用MSVC 2019编译
- [ ] 安装到 `thirdparty/cpprestsdk/{include,lib,bin}/`

#### Boost库
- [ ] 下载: `git clone --recursive https://github.com/boostorg/boost.git external/boost`
- [ ] 运行bootstrap.bat
- [ ] 使用MSVC 2019编译所需模块 (system, filesystem, thread等)
- [ ] 安装到 `thirdparty/boost/{include,lib,bin}/`

### 2.5 可选高级功能依赖

#### PyTorch C++ (libtorch)
- [ ] 下载MSVC版本: libtorch-win-shared-with-deps-1.1.0.zip
- [ ] 解压到 `external/libtorch`
- [ ] 复制头文件和库到 `thirdparty/libtorch/{include,lib,bin}/`

#### CUDA Toolkit
- [ ] 验证CUDA >= 10.0安装
- [ ] 确认CUDA支持MSVC 2019
- [ ] 配置CUDA环境变量
- [ ] 测试CUDA编译器可用性

---

## 阶段3：CMake配置文件修改

### 3.1 修改主CMakeLists.txt
- [ ] 添加每个依赖库的路径到CMAKE_PREFIX_PATH:
  ```cmake
  list(APPEND CMAKE_PREFIX_PATH 
    "${CMAKE_SOURCE_DIR}/thirdparty/oneTBB"
    "${CMAKE_SOURCE_DIR}/thirdparty/OpenIGTLink"
    "${CMAKE_SOURCE_DIR}/thirdparty/ITK"
    "${CMAKE_SOURCE_DIR}/thirdparty/cpprestsdk"
    "${CMAKE_SOURCE_DIR}/thirdparty/boost"
    "${CMAKE_SOURCE_DIR}/thirdparty/libtorch"
  )
  ```
- [ ] 配置所有SUPRA选项为ON

### 3.2 创建自定义Find模块
- [ ] 创建 `cmake/FindThirdPartyLibs.cmake`
- [ ] 实现统一的第三方库查找逻辑
- [ ] 处理不同配置下的库路径

### 3.3 修改TBB查找逻辑
- [ ] 修改 `src/SupraLib/CMakeLists.txt` 中的TBB路径逻辑
- [ ] 将硬编码的 `vc14` 路径改为通用路径
- [ ] 支持从 `thirdparty/oneTBB/` 查找TBB

### 3.4 修改各子模块的CMakeLists.txt
- [ ] 修改 `src/GraphicInterface/CMakeLists.txt`
- [ ] 修改 `src/RestInterface/CMakeLists.txt`
- [ ] 确保所有依赖都从对应的 `thirdparty/` 子目录查找

---

## 阶段4：编译配置和构建

### 4.1 清理和重新配置
- [ ] 清理现有build目录: `rmdir /s /q build && mkdir build`
- [ ] 设置MSVC 2019环境变量
- [ ] 配置CMake使用MSVC编译器:
  ```bash
  cd build
  cmake -G "Visual Studio 16 2019" -A x64 ..
  ```
- [ ] 验证所有依赖库都被正确找到

### 4.2 使用CMake直接编译项目 🔧
**重要**: 使用CMake命令行进行构建，不使用Visual Studio IDE
- [ ] 编译Release版本:
  ```bash
  cmake --build . --config Release --parallel
  ```
- [ ] 编译Debug版本 (可选):
  ```bash
  cmake --build . --config Debug --parallel
  ```
- [ ] 验证编译结果:
  - [ ] 检查SupraLib核心库是否生成
  - [ ] 检查图形界面 (SUPRA_GUI) 可执行文件
  - [ ] 检查命令行界面可执行文件
  - [ ] 检查REST接口可执行文件
  - [ ] 检查所有启用的模块

### 4.3 解决编译错误
- [ ] 处理头文件包含问题
- [ ] 解决链接器错误
- [ ] 修复CUDA编译问题 (确保使用MSVC)
- [ ] 处理Qt相关编译问题
- [ ] 使用CMake详细输出调试编译问题:
  ```bash
  cmake --build . --config Release --parallel --verbose
  ```

---

## 阶段5：测试和验证

### 5.1 功能测试
- [ ] 测试图形界面启动
- [ ] 测试命令行界面
- [ ] 测试REST接口
- [ ] 验证CUDA功能
- [ ] 测试示例数据处理

### 5.2 集成测试
- [ ] 运行demo配置: `SUPRA_GUI -c data/configDemo.xml -a`
- [ ] 运行3D demo: `SUPRA_GUI -c data/configDemo3D.xml -a`
- [ ] 验证所有启用的功能模块

---

## 阶段6：优化和文档

### 6.1 创建依赖管理脚本
- [ ] 编写 `build_dependencies.bat` 脚本 (使用CMake构建所有依赖)
- [ ] 创建一键式构建解决方案:
  ```batch
  @echo off
  echo Building all dependencies with CMake...
  call build_dependencies.bat
  echo Configuring SUPRA project...
  cd build
  cmake -G "Visual Studio 16 2019" -A x64 ..
  echo Building SUPRA project...
  cmake --build . --config Release --parallel
  echo Build completed!
  ```
- [ ] 添加错误处理和日志记录

### 6.2 更新构建文档
- [ ] 记录完整的构建过程
- [ ] 提供故障排除指南
- [ ] 创建开发者文档

---

## 预期的最终目录结构

```
e:/project/supra/
├── external/                    # 第三方库源码
│   ├── oneTBB/                 # Intel TBB源码
│   ├── OpenIGTLink/            # OpenIGTLink源码
│   ├── ITK/                    # ITK源码
│   ├── cpprestsdk/             # C++ REST SDK源码
│   ├── boost/                  # Boost库源码
│   └── libtorch/               # PyTorch C++库
├── thirdparty/                 # 编译后的第三方库
│   ├── oneTBB/                 # TBB库
│   │   ├── include/
│   │   ├── lib/
│   │   └── bin/
│   ├── OpenIGTLink/            # OpenIGTLink库
│   │   ├── include/
│   │   ├── lib/
│   │   └── bin/
│   ├── ITK/                    # ITK库
│   │   ├── include/
│   │   ├── lib/
│   │   └── bin/
│   ├── cpprestsdk/             # C++ REST SDK
│   │   ├── include/
│   │   ├── lib/
│   │   └── bin/
│   ├── boost/                  # Boost库
│   │   ├── include/
│   │   ├── lib/
│   │   └── bin/
│   └── libtorch/               # PyTorch C++库
│       ├── include/
│       ├── lib/
│       └── bin/
├── build/                      # 构建目录
├── src/                        # SUPRA源代码
├── cmake/                      # CMake配置文件
├── config/                     # 配置文件
├── data/                       # 示例数据
├── build_dependencies.bat      # 依赖构建脚本
└── SUPRA_编译计划_更新版.md    # 本计划文件
```

---

## 关键成功因素

1. **编译器一致性**: 所有库都必须使用MSVC 2019编译 ⚠️
2. **构建系统**: 使用CMake命令行进行构建，不依赖Visual Studio IDE 🔧
3. **CUDA兼容性**: 确保CUDA版本支持MSVC 2019
4. **依赖版本兼容性**: 确保所有依赖库版本相互兼容
5. **路径管理**: 按依赖库分别管理thirdparty目录
6. **错误处理**: 及时处理编译过程中的各种错误
7. **测试验证**: 每个阶段都进行充分的测试验证

---

## 预计时间安排

- 阶段1: 0.5小时 (目录创建和分析)
- 阶段2: 4-6小时 (依赖下载和编译，MSVC编译时间较长)
- 阶段3: 1-1.5小时 (CMake配置修改)
- 阶段4: 1-2小时 (项目编译)
- 阶段5: 0.5小时 (测试验证)
- 阶段6: 0.5小时 (优化文档)

**总计**: 约7-10小时

---

## 重要提醒

⚠️ **必须使用MSVC 2019编译器**
- MinGW不兼容CUDA
- 第三方库都是MSVC编译的
- 避免ABI兼容性问题

🔧 **使用CMake命令行构建**
- 不使用Visual Studio IDE进行构建
- 使用 `cmake --build` 命令进行编译
- 支持并行编译提高构建速度
- 便于自动化和脚本化

✅ **按依赖库分别管理thirdparty目录**
- 每个库有独立的include/lib/bin目录
- 便于版本管理和问题排查
- 支持增量更新

*本计划将确保SUPRA项目的完整编译，启用所有功能，并提供可维护的依赖管理方案。*