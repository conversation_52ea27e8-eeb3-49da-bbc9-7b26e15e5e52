﻿<?xml version="1.0" encoding="utf-8"?>
<modelStoreModel xmlns:dm0="http://schemas.microsoft.com/VisualStudio/2008/DslTools/Core" xmlns:dm1="http://schemas.microsoft.com/dsltools/Kernel" xmlns:dm2="http://schemas.microsoft.com/dsltools/Component" xmlns:dm3="http://schemas.microsoft.com/dsltools/Activity" xmlns:dm4="http://schemas.microsoft.com/dsltools/UseCase" xmlns:dm5="http://schemas.microsoft.com/dsltools/Interaction" xmlns:dm6="http://schemas.microsoft.com/dsltools/UmlModelLibrary" xmlns:dm7="http://schemas.microsoft.com/dsltools/UmlDiagrams" xmlns:dm8="http://schemas.microsoft.com/dsltools/LogicalClassDesigner" xmlns:dm9="http://schemas.microsoft.com/VisualStudio/TeamArchitect/SequenceDesigner"
  dslVersion="1.0.0.0"
  Id="d8783d0d-013f-4725-987e-02e7c63fac39"
  name="models-camp-us2" xmlns="http://schemas.microsoft.com/dsltools/ModelStore">
  <profileInstances>
    <packageHasProfileInstances
      Id="0caec977-1f8c-4ba3-a7db-8cc9ad9cc73b">
      <profileInstance
        Id="e34d544e-0fea-4ed6-ac5e-1b74119ac791"
        name="StandardProfileL2" />
    </packageHasProfileInstances>
    <packageHasProfileInstances
      Id="29349502-908c-4fda-9054-c48619c59ed0">
      <profileInstance
        Id="532ea607-fb19-44b8-8502-3351b05452be"
        name="StandardProfileL3" />
    </packageHasProfileInstances>
  </profileInstances>
  <packagedElements>
    <packageHasNamedElement>
      <interaction
        Id="f60a5bc8-b7f0-48ad-b6db-9b8cd37f7c90"
        name="SequenceInputDeviceInit"
        collapseFragmentsFlag="false"
        isActiveClass="false"
        isAbstract="false"
        isLeaf="false"
        isReentrant="false">
        <fragments>
          <behaviorExecutionSpecification
            Id="a3b1cd2d-140a-4642-bd3c-7628eebc0199"
            name="BehaviorExecutionSpecification1">
            <coveredLifelines>
              <lifelineMoniker
                Id="170bfc6a-c1b2-41e1-ab99-2dd645d45d49"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="544b6a23-b026-44d1-aa9f-bf3d3d016c6d"
                LastKnownName="ExecutionOccurrenceSpecification2"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="9b20d9e8-4798-4e1b-bc52-faf1c626f440"
                LastKnownName="ExecutionOccurrenceSpecification1"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="c3687846-79c1-411b-a079-8e111295a492"
                LastKnownName="MessageOccurrenceSpecification2"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="58fdaa12-6aff-414a-bdac-8eccc6ccdcb3"
                LastKnownName="MessageOccurrenceSpecification3"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="9b20d9e8-4798-4e1b-bc52-faf1c626f440"
            name="ExecutionOccurrenceSpecification1">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="62eece2b-b709-4a1f-ae0e-4ceebfd2d0a3"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="170bfc6a-c1b2-41e1-ab99-2dd645d45d49"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="415d7037-7702-4fde-9b23-d658e4c48c3e"
            name="MessageOccurrenceSpecification1">
            <covered>
              <lifelineMoniker
                Id="1c5d10a1-d37a-4b57-a5c8-24fee093aaf9"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="c3687846-79c1-411b-a079-8e111295a492"
            name="MessageOccurrenceSpecification2">
            <covered>
              <lifelineMoniker
                Id="170bfc6a-c1b2-41e1-ab99-2dd645d45d49"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="58fdaa12-6aff-414a-bdac-8eccc6ccdcb3"
            name="MessageOccurrenceSpecification3">
            <covered>
              <lifelineMoniker
                Id="170bfc6a-c1b2-41e1-ab99-2dd645d45d49"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="83c14c2c-e66f-4955-9960-0a15bff619ce"
            name="MessageOccurrenceSpecification4">
            <covered>
              <lifelineMoniker
                Id="1c5d10a1-d37a-4b57-a5c8-24fee093aaf9"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="544b6a23-b026-44d1-aa9f-bf3d3d016c6d"
            name="ExecutionOccurrenceSpecification2">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="ef870f28-f291-46af-818c-40dce209c557"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="170bfc6a-c1b2-41e1-ab99-2dd645d45d49"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="77cdb31f-0d3f-4e16-b106-d831e8d59793"
            name="BehaviorExecutionSpecification2">
            <coveredLifelines>
              <lifelineMoniker
                Id="170bfc6a-c1b2-41e1-ab99-2dd645d45d49"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="921be95a-de3d-49c2-b04e-45a8c24b5490"
                LastKnownName="ExecutionOccurrenceSpecification4"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="854a73e8-6aef-47f1-8abb-19bdf81e9c44"
                LastKnownName="ExecutionOccurrenceSpecification3"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="cdd99733-b10d-46b2-a7b0-11732acf1257"
                LastKnownName="MessageOccurrenceSpecification6"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="f2b49368-466a-48e0-bf85-7af4b513eeb3"
                LastKnownName="MessageOccurrenceSpecification9"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="56d8f581-debb-4da6-a8ce-2f08e81e8aca"
                LastKnownName="MessageOccurrenceSpecification12"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="35f1bd1e-427a-43ba-8b79-9a3bae7762fe"
                LastKnownName="MessageOccurrenceSpecification7"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="854a73e8-6aef-47f1-8abb-19bdf81e9c44"
            name="ExecutionOccurrenceSpecification3">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="a5e3fb3d-70bc-466a-a0c8-3e70840104fc"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="170bfc6a-c1b2-41e1-ab99-2dd645d45d49"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="cdd99733-b10d-46b2-a7b0-11732acf1257"
            name="MessageOccurrenceSpecification6">
            <covered>
              <lifelineMoniker
                Id="170bfc6a-c1b2-41e1-ab99-2dd645d45d49"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="67cd8791-bc76-4ba9-8336-1fe7cf5cddb5"
            name="MessageOccurrenceSpecification5">
            <covered>
              <lifelineMoniker
                Id="1c5d10a1-d37a-4b57-a5c8-24fee093aaf9"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="2d34a7c0-e662-43d1-b923-5c7a4d8eaa18"
            name="BehaviorExecutionSpecification3">
            <coveredLifelines>
              <lifelineMoniker
                Id="71187740-58d9-4407-a9a6-26cd9df702d4"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="1e27149e-6545-4648-b49a-5af9017aacef"
                LastKnownName="ExecutionOccurrenceSpecification6"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="98761098-36b5-4dc3-92be-37b950f88558"
                LastKnownName="ExecutionOccurrenceSpecification5"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="752b28d9-abfd-4961-91a9-7d820a593a8c"
                LastKnownName="MessageOccurrenceSpecification10"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="2c1b3a98-2101-42bd-b3f3-905aa347ee61"
                LastKnownName="MessageOccurrenceSpecification11"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="98761098-36b5-4dc3-92be-37b950f88558"
            name="ExecutionOccurrenceSpecification5">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="1ad3698a-d2a6-47b7-bb4c-ce2cf6474854"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="71187740-58d9-4407-a9a6-26cd9df702d4"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="752b28d9-abfd-4961-91a9-7d820a593a8c"
            name="MessageOccurrenceSpecification10">
            <covered>
              <lifelineMoniker
                Id="71187740-58d9-4407-a9a6-26cd9df702d4"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="f2b49368-466a-48e0-bf85-7af4b513eeb3"
            name="MessageOccurrenceSpecification9">
            <covered>
              <lifelineMoniker
                Id="170bfc6a-c1b2-41e1-ab99-2dd645d45d49"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="2c1b3a98-2101-42bd-b3f3-905aa347ee61"
            name="MessageOccurrenceSpecification11">
            <covered>
              <lifelineMoniker
                Id="71187740-58d9-4407-a9a6-26cd9df702d4"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="56d8f581-debb-4da6-a8ce-2f08e81e8aca"
            name="MessageOccurrenceSpecification12">
            <covered>
              <lifelineMoniker
                Id="170bfc6a-c1b2-41e1-ab99-2dd645d45d49"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="1e27149e-6545-4648-b49a-5af9017aacef"
            name="ExecutionOccurrenceSpecification6">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="c1f6a6bf-937b-493f-954c-32ff8ebe6b86"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="71187740-58d9-4407-a9a6-26cd9df702d4"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="35f1bd1e-427a-43ba-8b79-9a3bae7762fe"
            name="MessageOccurrenceSpecification7">
            <covered>
              <lifelineMoniker
                Id="170bfc6a-c1b2-41e1-ab99-2dd645d45d49"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="09e99ff9-ca19-4f93-937a-9a9be0ad9a51"
            name="MessageOccurrenceSpecification8">
            <covered>
              <lifelineMoniker
                Id="1c5d10a1-d37a-4b57-a5c8-24fee093aaf9"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="921be95a-de3d-49c2-b04e-45a8c24b5490"
            name="ExecutionOccurrenceSpecification4">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="4dac9de4-bd4a-4a58-b3b6-c735ac3ba7f3"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="170bfc6a-c1b2-41e1-ab99-2dd645d45d49"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="00999e0a-8e93-49d5-9b6e-8e3f81cefe25"
            name="BehaviorExecutionSpecification4">
            <coveredLifelines>
              <lifelineMoniker
                Id="71187740-58d9-4407-a9a6-26cd9df702d4"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="32cff5bb-1a59-47e8-9150-cbed8f49a310"
                LastKnownName="ExecutionOccurrenceSpecification8"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="34875fc4-4a46-42ed-9e60-7fb5558794eb"
                LastKnownName="ExecutionOccurrenceSpecification7"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="ba7d233c-aaa1-4f24-bbe7-e8373c2455db"
                LastKnownName="MessageOccurrenceSpecification14"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="88220b04-0c0f-4590-95b5-4441108459fa"
                LastKnownName="MessageOccurrenceSpecification15"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="34875fc4-4a46-42ed-9e60-7fb5558794eb"
            name="ExecutionOccurrenceSpecification7">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="052fc737-7417-4aa9-92b4-bfac15d64fec"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="71187740-58d9-4407-a9a6-26cd9df702d4"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="a7ac29bb-1c5d-4def-9f94-0feee4ad4455"
            name="MessageOccurrenceSpecification13">
            <covered>
              <lifelineMoniker
                Id="1c5d10a1-d37a-4b57-a5c8-24fee093aaf9"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="ba7d233c-aaa1-4f24-bbe7-e8373c2455db"
            name="MessageOccurrenceSpecification14">
            <covered>
              <lifelineMoniker
                Id="71187740-58d9-4407-a9a6-26cd9df702d4"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="88220b04-0c0f-4590-95b5-4441108459fa"
            name="MessageOccurrenceSpecification15">
            <covered>
              <lifelineMoniker
                Id="71187740-58d9-4407-a9a6-26cd9df702d4"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="f36deab9-770c-4629-9c03-307caba3f494"
            name="MessageOccurrenceSpecification16">
            <covered>
              <lifelineMoniker
                Id="1c5d10a1-d37a-4b57-a5c8-24fee093aaf9"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="32cff5bb-1a59-47e8-9150-cbed8f49a310"
            name="ExecutionOccurrenceSpecification8">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="75013a90-d089-4dee-b36a-5a5bc37cf8c6"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="71187740-58d9-4407-a9a6-26cd9df702d4"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="0da46eda-9dfa-41e5-acd0-9a0fa45da8f6"
            name="BehaviorExecutionSpecification5">
            <coveredLifelines>
              <lifelineMoniker
                Id="71187740-58d9-4407-a9a6-26cd9df702d4"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="4e6a272f-839a-4b19-ad25-b9d10cedff63"
                LastKnownName="ExecutionOccurrenceSpecification10"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="22e330da-6256-44ba-a1e9-c99d017f9bbf"
                LastKnownName="ExecutionOccurrenceSpecification9"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="19406846-8a6e-45c1-821b-83d5eb0bdb3f"
                LastKnownName="MessageOccurrenceSpecification18"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="d5381025-dc68-4b71-b0c4-6651a6c1722a"
                LastKnownName="MessageOccurrenceSpecification19"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="22e330da-6256-44ba-a1e9-c99d017f9bbf"
            name="ExecutionOccurrenceSpecification9">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="caab9817-7a31-444f-80d8-299d808e63ee"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="71187740-58d9-4407-a9a6-26cd9df702d4"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="6e1d500a-f4b0-4a2f-874e-ae49fe69ada4"
            name="MessageOccurrenceSpecification17">
            <covered>
              <lifelineMoniker
                Id="1c5d10a1-d37a-4b57-a5c8-24fee093aaf9"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="19406846-8a6e-45c1-821b-83d5eb0bdb3f"
            name="MessageOccurrenceSpecification18">
            <covered>
              <lifelineMoniker
                Id="71187740-58d9-4407-a9a6-26cd9df702d4"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="b094861f-aac5-478d-b8dc-6c46b6bfb366"
            name="MessageOccurrenceSpecification20">
            <covered>
              <lifelineMoniker
                Id="1c5d10a1-d37a-4b57-a5c8-24fee093aaf9"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="d5381025-dc68-4b71-b0c4-6651a6c1722a"
            name="MessageOccurrenceSpecification19">
            <covered>
              <lifelineMoniker
                Id="71187740-58d9-4407-a9a6-26cd9df702d4"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="4e6a272f-839a-4b19-ad25-b9d10cedff63"
            name="ExecutionOccurrenceSpecification10">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="0df150ed-b73f-415a-909e-a2d2090a5bf0"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="71187740-58d9-4407-a9a6-26cd9df702d4"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
        </fragments>
        <lifelines>
          <lifeline
            Id="1c5d10a1-d37a-4b57-a5c8-24fee093aaf9"
            name="Controller"
            isActor="false"
            lifelineDisplayName="Controller">
            <topLevelOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="415d7037-7702-4fde-9b23-d658e4c48c3e"
                LastKnownName="MessageOccurrenceSpecification1"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="83c14c2c-e66f-4955-9960-0a15bff619ce"
                LastKnownName="MessageOccurrenceSpecification4"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="67cd8791-bc76-4ba9-8336-1fe7cf5cddb5"
                LastKnownName="MessageOccurrenceSpecification5"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="09e99ff9-ca19-4f93-937a-9a9be0ad9a51"
                LastKnownName="MessageOccurrenceSpecification8"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="a7ac29bb-1c5d-4def-9f94-0feee4ad4455"
                LastKnownName="MessageOccurrenceSpecification13"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="f36deab9-770c-4629-9c03-307caba3f494"
                LastKnownName="MessageOccurrenceSpecification16"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="6e1d500a-f4b0-4a2f-874e-ae49fe69ada4"
                LastKnownName="MessageOccurrenceSpecification17"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="b094861f-aac5-478d-b8dc-6c46b6bfb366"
                LastKnownName="MessageOccurrenceSpecification20"
                LastKnownLocation="models-camp-us2.uml" />
            </topLevelOccurrences>
          </lifeline>
          <lifeline
            Id="170bfc6a-c1b2-41e1-ab99-2dd645d45d49"
            name=": AbstractInput"
            isActor="false"
            lifelineDisplayName=": AbstractInput">
            <topLevelOccurrences>
              <executionOccurrenceSpecificationMoniker
                Id="9b20d9e8-4798-4e1b-bc52-faf1c626f440"
                LastKnownName="ExecutionOccurrenceSpecification1"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="544b6a23-b026-44d1-aa9f-bf3d3d016c6d"
                LastKnownName="ExecutionOccurrenceSpecification2"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="854a73e8-6aef-47f1-8abb-19bdf81e9c44"
                LastKnownName="ExecutionOccurrenceSpecification3"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="921be95a-de3d-49c2-b04e-45a8c24b5490"
                LastKnownName="ExecutionOccurrenceSpecification4"
                LastKnownLocation="models-camp-us2.uml" />
            </topLevelOccurrences>
          </lifeline>
          <lifeline
            Id="71187740-58d9-4407-a9a6-26cd9df702d4"
            name=": InputDevice:AbstractInput"
            isActor="false"
            lifelineDisplayName=": InputDevice:AbstractInput">
            <topLevelOccurrences>
              <executionOccurrenceSpecificationMoniker
                Id="98761098-36b5-4dc3-92be-37b950f88558"
                LastKnownName="ExecutionOccurrenceSpecification5"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="1e27149e-6545-4648-b49a-5af9017aacef"
                LastKnownName="ExecutionOccurrenceSpecification6"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="34875fc4-4a46-42ed-9e60-7fb5558794eb"
                LastKnownName="ExecutionOccurrenceSpecification7"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="32cff5bb-1a59-47e8-9150-cbed8f49a310"
                LastKnownName="ExecutionOccurrenceSpecification8"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="22e330da-6256-44ba-a1e9-c99d017f9bbf"
                LastKnownName="ExecutionOccurrenceSpecification9"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="4e6a272f-839a-4b19-ad25-b9d10cedff63"
                LastKnownName="ExecutionOccurrenceSpecification10"
                LastKnownLocation="models-camp-us2.uml" />
            </topLevelOccurrences>
          </lifeline>
        </lifelines>
        <messages>
          <message
            Id="b7d89d43-e94b-41b4-99b0-3a27a2a2603a"
            name="new"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="415d7037-7702-4fde-9b23-d658e4c48c3e"
                LastKnownName="MessageOccurrenceSpecification1"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="c3687846-79c1-411b-a079-8e111295a492"
                LastKnownName="MessageOccurrenceSpecification2"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="05931c27-8c58-4cee-96d3-61332489b40d"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="58fdaa12-6aff-414a-bdac-8eccc6ccdcb3"
                LastKnownName="MessageOccurrenceSpecification3"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="83c14c2c-e66f-4955-9960-0a15bff619ce"
                LastKnownName="MessageOccurrenceSpecification4"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="d0705c46-6b18-4d96-a992-0be51445544d"
            name="changeConfig(dictionary)"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="67cd8791-bc76-4ba9-8336-1fe7cf5cddb5"
                LastKnownName="MessageOccurrenceSpecification5"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="cdd99733-b10d-46b2-a7b0-11732acf1257"
                LastKnownName="MessageOccurrenceSpecification6"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="3cd9053f-c0d9-4194-873a-6f8605224368"
            name="configurationChanged()"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="f2b49368-466a-48e0-bf85-7af4b513eeb3"
                LastKnownName="MessageOccurrenceSpecification9"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="752b28d9-abfd-4961-91a9-7d820a593a8c"
                LastKnownName="MessageOccurrenceSpecification10"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="1277bf1a-2f52-4785-aa93-339607281786"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="2c1b3a98-2101-42bd-b3f3-905aa347ee61"
                LastKnownName="MessageOccurrenceSpecification11"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="56d8f581-debb-4da6-a8ce-2f08e81e8aca"
                LastKnownName="MessageOccurrenceSpecification12"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="50d617b2-5b57-4b12-841c-339d68bff912"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="35f1bd1e-427a-43ba-8b79-9a3bae7762fe"
                LastKnownName="MessageOccurrenceSpecification7"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="09e99ff9-ca19-4f93-937a-9a9be0ad9a51"
                LastKnownName="MessageOccurrenceSpecification8"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="1b894da6-6f1e-4362-a83b-32b30f66f342"
            name="initializeDevice()"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="a7ac29bb-1c5d-4def-9f94-0feee4ad4455"
                LastKnownName="MessageOccurrenceSpecification13"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="ba7d233c-aaa1-4f24-bbe7-e8373c2455db"
                LastKnownName="MessageOccurrenceSpecification14"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="2b170ed3-9499-4127-bfc0-ec5a1484a40a"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="88220b04-0c0f-4590-95b5-4441108459fa"
                LastKnownName="MessageOccurrenceSpecification15"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="f36deab9-770c-4629-9c03-307caba3f494"
                LastKnownName="MessageOccurrenceSpecification16"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="eb04207d-a7e1-42f0-a49c-42dbc7d6166a"
            name="ready()"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="6e1d500a-f4b0-4a2f-874e-ae49fe69ada4"
                LastKnownName="MessageOccurrenceSpecification17"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="19406846-8a6e-45c1-821b-83d5eb0bdb3f"
                LastKnownName="MessageOccurrenceSpecification18"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="ff0d8cad-4889-4eac-a92d-794da96b3324"
            name="bool"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="d5381025-dc68-4b71-b0c4-6651a6c1722a"
                LastKnownName="MessageOccurrenceSpecification19"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="b094861f-aac5-478d-b8dc-6c46b6bfb366"
                LastKnownName="MessageOccurrenceSpecification20"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
        </messages>
      </interaction>
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="62eece2b-b709-4a1f-ae0e-4ceebfd2d0a3"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="ef870f28-f291-46af-818c-40dce209c557"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="a5e3fb3d-70bc-466a-a0c8-3e70840104fc"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="4dac9de4-bd4a-4a58-b3b6-c735ac3ba7f3"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="1ad3698a-d2a6-47b7-bb4c-ce2cf6474854"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="c1f6a6bf-937b-493f-954c-32ff8ebe6b86"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="052fc737-7417-4aa9-92b4-bfac15d64fec"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="75013a90-d089-4dee-b36a-5a5bc37cf8c6"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="caab9817-7a31-444f-80d8-299d808e63ee"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="0df150ed-b73f-415a-909e-a2d2090a5bf0"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <interaction
        Id="80f61d93-b540-46a5-836c-7b5cfa91710a"
        name="SequenceInputStreamTimer"
        collapseFragmentsFlag="false"
        isActiveClass="false"
        isAbstract="false"
        isLeaf="false"
        isReentrant="false">
        <fragments>
          <behaviorExecutionSpecification
            Id="5b619200-d199-46b6-8812-cbe6b545c7c7"
            name="BehaviorExecutionSpecification6">
            <coveredLifelines>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="0a0c0b8e-d3a3-43ee-8f18-9b5e6d6022c7"
                LastKnownName="ExecutionOccurrenceSpecification12"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="b3ee0a94-f9ab-434b-beca-dbd1559a9077"
                LastKnownName="ExecutionOccurrenceSpecification11"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="96ea4439-7568-4e98-b000-17b5c24a3b82"
                LastKnownName="MessageOccurrenceSpecification22"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="4ba7cfaf-24fa-4b6b-ac1e-ceaad340f9d2"
                LastKnownName="MessageOccurrenceSpecification23"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="4f164429-7c03-4c64-b110-07711053d812"
                LastKnownName="ExecutionOccurrenceSpecification13"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="4360a3a4-787c-4cf3-b20a-0805718558c3"
                LastKnownName="ExecutionOccurrenceSpecification14"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="1aaa6cd8-2661-4775-a0b3-b1a2619f5290"
                LastKnownName="MessageOccurrenceSpecification25"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="e8210100-5a46-4211-a1da-d0f1a1ffb5ab"
                LastKnownName="ExecutionOccurrenceSpecification17"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="3243b3e6-e751-4ad1-af28-55bf60a32d32"
                LastKnownName="ExecutionOccurrenceSpecification18"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="13ba8070-e7fa-471c-8080-1a5335583815"
                LastKnownName="ExecutionOccurrenceSpecification19"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="64fe7467-1093-48c6-81b9-0562fca23244"
                LastKnownName="ExecutionOccurrenceSpecification20"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="e1d4f946-938b-4d80-9e31-f7d65df0d635"
                LastKnownName="MessageOccurrenceSpecification28"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="b3ee0a94-f9ab-434b-beca-dbd1559a9077"
            name="ExecutionOccurrenceSpecification11">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="a39a0443-a177-4312-9e3c-778137907205"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="96ea4439-7568-4e98-b000-17b5c24a3b82"
            name="MessageOccurrenceSpecification22">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="ebc065c4-14a5-4f06-b1d4-1cf7dbaabd53"
            name="MessageOccurrenceSpecification21">
            <covered>
              <lifelineMoniker
                Id="168db48c-61d4-4e47-9b96-530868c97e2a"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="4ba7cfaf-24fa-4b6b-ac1e-ceaad340f9d2"
            name="MessageOccurrenceSpecification23">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="deb5bdcc-58ff-4c59-94ab-95b21ee155c3"
            name="BehaviorExecutionSpecification7">
            <coveredLifelines>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="4360a3a4-787c-4cf3-b20a-0805718558c3"
                LastKnownName="ExecutionOccurrenceSpecification14"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="4f164429-7c03-4c64-b110-07711053d812"
                LastKnownName="ExecutionOccurrenceSpecification13"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="97bbcae6-ba52-45a1-9faf-d19d0debbcf8"
                LastKnownName="MessageOccurrenceSpecification24"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="4f164429-7c03-4c64-b110-07711053d812"
            name="ExecutionOccurrenceSpecification13">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="cc747941-67a8-46e2-8711-658fc8ff02d0"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="97bbcae6-ba52-45a1-9faf-d19d0debbcf8"
            name="MessageOccurrenceSpecification24">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="4360a3a4-787c-4cf3-b20a-0805718558c3"
            name="ExecutionOccurrenceSpecification14">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="4bdbe986-afa4-44b5-a827-d0fba2a84b05"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="026d925f-ba6d-465e-b031-3a7f72c8a43e"
            name="BehaviorExecutionSpecification8">
            <coveredLifelines>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="0e7d9e15-9f57-4ccb-a605-b7cfa9ff22a7"
                LastKnownName="ExecutionOccurrenceSpecification16"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="4733b52a-5a45-442a-a3ca-11b1b4241e91"
                LastKnownName="ExecutionOccurrenceSpecification15"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="b1e08dad-2ed7-4678-bce8-232732508b56"
                LastKnownName="MessageOccurrenceSpecification26"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="eeb5eb54-f25a-4fb9-b09d-2119f9442d35"
                LastKnownName="MessageOccurrenceSpecification29"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="cbaac0e0-ba93-4606-8a69-e7581994f410"
                LastKnownName="MessageOccurrenceSpecification32"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="4ec02fda-802a-42fc-9b0c-441fa6547a70"
                LastKnownName="MessageOccurrenceSpecification33"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="8de7a68d-5fcc-4d21-a0f6-a086861c67e4"
                LastKnownName="OperandOccurrenceSpecification1"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="d381d124-6eef-4309-8eeb-3103e84a7952"
                LastKnownName="ExecutionOccurrenceSpecification21"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="f0375f1b-905a-4f25-ac0b-cc762bdcffdb"
                LastKnownName="ExecutionOccurrenceSpecification22"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="9a230c52-ce60-48b7-9a66-b51f2600db89"
                LastKnownName="OperandOccurrenceSpecification2"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="efdf61b6-6617-49aa-b390-d94b0386e476"
                LastKnownName="MessageOccurrenceSpecification36"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="d6b205fc-2593-442a-8750-f29298f1e29e"
                LastKnownName="MessageOccurrenceSpecification27"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="4733b52a-5a45-442a-a3ca-11b1b4241e91"
            name="ExecutionOccurrenceSpecification15">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="1b108df5-a7e9-478c-8754-7bbe65477239"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="b1e08dad-2ed7-4678-bce8-232732508b56"
            name="MessageOccurrenceSpecification26">
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="1aaa6cd8-2661-4775-a0b3-b1a2619f5290"
            name="MessageOccurrenceSpecification25">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="6e8d1c74-1bdb-42ee-9500-7546eeb72f79"
            name="BehaviorExecutionSpecification9">
            <coveredLifelines>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="3243b3e6-e751-4ad1-af28-55bf60a32d32"
                LastKnownName="ExecutionOccurrenceSpecification18"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="e8210100-5a46-4211-a1da-d0f1a1ffb5ab"
                LastKnownName="ExecutionOccurrenceSpecification17"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="8f05553c-d437-4ffc-bf4f-d981eca82993"
                LastKnownName="MessageOccurrenceSpecification30"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="2aa80dbb-7e9d-4304-b478-a0bb5dd35001"
                LastKnownName="MessageOccurrenceSpecification45"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="c6b6c6f6-bfe4-4c12-ab0b-07c40fe31eb8"
                LastKnownName="MessageOccurrenceSpecification48"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="ceffa0d1-a141-452b-aa52-85d30f1fdbab"
                LastKnownName="MessageOccurrenceSpecification31"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="e8210100-5a46-4211-a1da-d0f1a1ffb5ab"
            name="ExecutionOccurrenceSpecification17">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="0086621b-a734-4d75-983e-e3f96ab0a391"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="8f05553c-d437-4ffc-bf4f-d981eca82993"
            name="MessageOccurrenceSpecification30">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="eeb5eb54-f25a-4fb9-b09d-2119f9442d35"
            name="MessageOccurrenceSpecification29">
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="2af549d0-a848-4cc6-82a8-1284a2fd53c3"
            name="BehaviorExecutionSpecification13">
            <coveredLifelines>
              <lifelineMoniker
                Id="5d36673f-ce2a-4fb7-9dca-7630242abc82"
                LastKnownName="SingleThreadTimer"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="37a4e05b-bd65-4d06-8c35-b6303296e17d"
                LastKnownName="ExecutionOccurrenceSpecification26"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="9c030e8d-990c-4f13-9ae0-5fe62358a444"
                LastKnownName="ExecutionOccurrenceSpecification25"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="3032c8ae-35e5-41bf-a6d7-05b56ec3d5b8"
                LastKnownName="MessageOccurrenceSpecification46"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="c4df4451-6bfa-4e43-8159-e064a66db019"
                LastKnownName="MessageOccurrenceSpecification47"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="9c030e8d-990c-4f13-9ae0-5fe62358a444"
            name="ExecutionOccurrenceSpecification25">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="31aae22a-b1ca-4037-ac4c-92d08a732beb"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="5d36673f-ce2a-4fb7-9dca-7630242abc82"
                LastKnownName="SingleThreadTimer"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="3032c8ae-35e5-41bf-a6d7-05b56ec3d5b8"
            name="MessageOccurrenceSpecification46">
            <covered>
              <lifelineMoniker
                Id="5d36673f-ce2a-4fb7-9dca-7630242abc82"
                LastKnownName="SingleThreadTimer"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="2aa80dbb-7e9d-4304-b478-a0bb5dd35001"
            name="MessageOccurrenceSpecification45">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="c4df4451-6bfa-4e43-8159-e064a66db019"
            name="MessageOccurrenceSpecification47">
            <covered>
              <lifelineMoniker
                Id="5d36673f-ce2a-4fb7-9dca-7630242abc82"
                LastKnownName="SingleThreadTimer"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="c6b6c6f6-bfe4-4c12-ab0b-07c40fe31eb8"
            name="MessageOccurrenceSpecification48">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="37a4e05b-bd65-4d06-8c35-b6303296e17d"
            name="ExecutionOccurrenceSpecification26">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="484cec9b-c203-4a6d-a530-152c20679144"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="5d36673f-ce2a-4fb7-9dca-7630242abc82"
                LastKnownName="SingleThreadTimer"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="cbaac0e0-ba93-4606-8a69-e7581994f410"
            name="MessageOccurrenceSpecification32">
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="ceffa0d1-a141-452b-aa52-85d30f1fdbab"
            name="MessageOccurrenceSpecification31">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="3243b3e6-e751-4ad1-af28-55bf60a32d32"
            name="ExecutionOccurrenceSpecification18">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="62665baf-e7f1-4456-a297-299fad6d3944"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="0ddf1322-07e0-4de8-9994-9665b5d3a2d8"
            name="BehaviorExecutionSpecification10">
            <coveredLifelines>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="64fe7467-1093-48c6-81b9-0562fca23244"
                LastKnownName="ExecutionOccurrenceSpecification20"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="13ba8070-e7fa-471c-8080-1a5335583815"
                LastKnownName="ExecutionOccurrenceSpecification19"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="d72b93cd-1642-4e9b-b38a-810f53518803"
                LastKnownName="MessageOccurrenceSpecification34"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="b2373ce4-6ff0-4a38-bd1b-22850d5f9761"
                LastKnownName="OperandOccurrenceSpecification3"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="c00e922b-cc50-433a-9325-65fc63ea1a1b"
                LastKnownName="MessageOccurrenceSpecification37"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="7e53a9cd-93bf-4292-9c7e-82db8124717e"
                LastKnownName="ExecutionOccurrenceSpecification33"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="2917886c-83c8-40a5-97fe-5260c60bff3c"
                LastKnownName="ExecutionOccurrenceSpecification34"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="c2d15ea5-0f4d-4a30-9b85-d3a12cf07302"
                LastKnownName="MessageOccurrenceSpecification40"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="3481d0a1-cde9-446e-a0c8-6e0b94ce8b93"
                LastKnownName="MessageOccurrenceSpecification41"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="6075a1b8-2880-4fff-a0ee-35073ab6e106"
                LastKnownName="MessageOccurrenceSpecification44"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="83456584-1366-4504-9087-f7b85a2a577d"
                LastKnownName="OperandOccurrenceSpecification4"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="c32a7b1b-5b67-4dcb-8b79-db3941fe4b84"
                LastKnownName="MessageOccurrenceSpecification35"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="13ba8070-e7fa-471c-8080-1a5335583815"
            name="ExecutionOccurrenceSpecification19">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="2bdeb1b2-adba-4136-a9f0-7f574cc40c02"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="d72b93cd-1642-4e9b-b38a-810f53518803"
            name="MessageOccurrenceSpecification34">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="4ec02fda-802a-42fc-9b0c-441fa6547a70"
            name="MessageOccurrenceSpecification33">
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <combinedFragment
            Id="10efc620-e194-48dd-8e24-27e95cb0b22d"
            name="CombinedFragment1"
            interactionOperator="Loop">
            <coveredLifelines>
              <lifelineMoniker
                Id="5d36673f-ce2a-4fb7-9dca-7630242abc82"
                LastKnownName="SingleThreadTimer"
                LastKnownLocation="models-camp-us2.uml" />
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <operands>
              <interactionOperand
                Id="0315debd-7173-4f34-8c66-d74b97816192"
                name="InteractionOperand1">
                <coveredLifelines>
                  <lifelineMoniker
                    Id="5d36673f-ce2a-4fb7-9dca-7630242abc82"
                    LastKnownName="SingleThreadTimer"
                    LastKnownLocation="models-camp-us2.uml" />
                  <lifelineMoniker
                    Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                    LastKnownName=": AbstractInput"
                    LastKnownLocation="models-camp-us2.uml" />
                  <lifelineMoniker
                    Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                    LastKnownName=": InputDevice:AbstractInput"
                    LastKnownLocation="models-camp-us2.uml" />
                </coveredLifelines>
                <guard>
                  <interactionConstraint
                    Id="029c03ba-7b4d-4343-bcd4-e582a953e1aa"
                    guardText="while(running)">
                    <maxInt>
                      <literalString
                        Id="8094a00f-b392-42c5-9dea-7c2b242b57a3"
                        name="LiteralString1" />
                    </maxInt>
                    <minInt>
                      <literalString
                        Id="042dcac1-8920-4aea-9f48-007e75692f48"
                        name="LiteralString2" />
                    </minInt>
                  </interactionConstraint>
                </guard>
                <operandOccurrenceSpecifications>
                  <operandOccurrenceSpecificationMoniker
                    Id="8de7a68d-5fcc-4d21-a0f6-a086861c67e4"
                    LastKnownName="OperandOccurrenceSpecification1"
                    LastKnownLocation="models-camp-us2.uml" />
                  <operandOccurrenceSpecificationMoniker
                    Id="9a230c52-ce60-48b7-9a66-b51f2600db89"
                    LastKnownName="OperandOccurrenceSpecification2"
                    LastKnownLocation="models-camp-us2.uml" />
                  <operandOccurrenceSpecificationMoniker
                    Id="b2373ce4-6ff0-4a38-bd1b-22850d5f9761"
                    LastKnownName="OperandOccurrenceSpecification3"
                    LastKnownLocation="models-camp-us2.uml" />
                  <operandOccurrenceSpecificationMoniker
                    Id="83456584-1366-4504-9087-f7b85a2a577d"
                    LastKnownName="OperandOccurrenceSpecification4"
                    LastKnownLocation="models-camp-us2.uml" />
                  <operandOccurrenceSpecificationMoniker
                    Id="a35efec6-a053-479f-95ae-4e9820efb05a"
                    LastKnownName="OperandOccurrenceSpecification5"
                    LastKnownLocation="models-camp-us2.uml" />
                  <operandOccurrenceSpecificationMoniker
                    Id="a19401c9-b7e1-4da8-ab2e-2912b871ab73"
                    LastKnownName="OperandOccurrenceSpecification6"
                    LastKnownLocation="models-camp-us2.uml" />
                </operandOccurrenceSpecifications>
              </interactionOperand>
            </operands>
          </combinedFragment>
          <operandOccurrenceSpecification
            Id="8de7a68d-5fcc-4d21-a0f6-a086861c67e4"
            name="OperandOccurrenceSpecification1">
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="b2373ce4-6ff0-4a38-bd1b-22850d5f9761"
            name="OperandOccurrenceSpecification3">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="a35efec6-a053-479f-95ae-4e9820efb05a"
            name="OperandOccurrenceSpecification5">
            <covered>
              <lifelineMoniker
                Id="5d36673f-ce2a-4fb7-9dca-7630242abc82"
                LastKnownName="SingleThreadTimer"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="41e93da4-597c-4c54-bf64-9ab04f6329b0"
            name="BehaviorExecutionSpecification11">
            <coveredLifelines>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="f0375f1b-905a-4f25-ac0b-cc762bdcffdb"
                LastKnownName="ExecutionOccurrenceSpecification22"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="d381d124-6eef-4309-8eeb-3103e84a7952"
                LastKnownName="ExecutionOccurrenceSpecification21"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="6718fdf4-2db3-43e6-a68f-9cb92066e533"
                LastKnownName="MessageOccurrenceSpecification38"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="02e518d4-578a-4616-9eaa-6cb4c8126f6b"
                LastKnownName="MessageOccurrenceSpecification55"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="e8795330-0e00-4606-a6b8-32e220893511"
                LastKnownName="ExecutionOccurrenceSpecification31"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="20b12bb7-ad8a-47de-b167-cd59e80a30a0"
                LastKnownName="ExecutionOccurrenceSpecification32"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="878ec1e9-abcf-477d-8de0-0423f6ebc747"
                LastKnownName="MessageOccurrenceSpecification57"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="c4a70f3f-816f-4d2d-91f6-cc5adf5f3c92"
                LastKnownName="MessageOccurrenceSpecification60"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="4dfef78e-ac5b-440b-b52e-711519aaffdd"
                LastKnownName="MessageOccurrenceSpecification39"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="d381d124-6eef-4309-8eeb-3103e84a7952"
            name="ExecutionOccurrenceSpecification21">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="6b544cda-6d05-4492-9a1b-7829ae5bbfec"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="c00e922b-cc50-433a-9325-65fc63ea1a1b"
            name="MessageOccurrenceSpecification37">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="6718fdf4-2db3-43e6-a68f-9cb92066e533"
            name="MessageOccurrenceSpecification38">
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="02e518d4-578a-4616-9eaa-6cb4c8126f6b"
            name="MessageOccurrenceSpecification55">
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="acdecad4-d0cb-48e5-b9e4-9f67c4e5b0ef"
            name="BehaviorExecutionSpecification16">
            <coveredLifelines>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="20b12bb7-ad8a-47de-b167-cd59e80a30a0"
                LastKnownName="ExecutionOccurrenceSpecification32"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="e8795330-0e00-4606-a6b8-32e220893511"
                LastKnownName="ExecutionOccurrenceSpecification31"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="009e13d3-acdb-4e90-af5f-a24b478e0069"
                LastKnownName="MessageOccurrenceSpecification56"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="e8795330-0e00-4606-a6b8-32e220893511"
            name="ExecutionOccurrenceSpecification31">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="54c42578-7f87-4d6d-afea-4b6fe020d6d5"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="009e13d3-acdb-4e90-af5f-a24b478e0069"
            name="MessageOccurrenceSpecification56">
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="20b12bb7-ad8a-47de-b167-cd59e80a30a0"
            name="ExecutionOccurrenceSpecification32">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="3bfd9d8b-e24d-4c1b-b1d9-952498dd177d"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="408874d3-dc4f-4ae1-a42a-5778bf659e39"
            name="BehaviorExecutionSpecification17">
            <coveredLifelines>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="2917886c-83c8-40a5-97fe-5260c60bff3c"
                LastKnownName="ExecutionOccurrenceSpecification34"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="7e53a9cd-93bf-4292-9c7e-82db8124717e"
                LastKnownName="ExecutionOccurrenceSpecification33"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="114a5665-1123-42be-8d4a-97a76d7eb902"
                LastKnownName="MessageOccurrenceSpecification58"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="f4a78726-a65a-4e6e-8a03-7e77a6850143"
                LastKnownName="MessageOccurrenceSpecification59"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="7e53a9cd-93bf-4292-9c7e-82db8124717e"
            name="ExecutionOccurrenceSpecification33">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="7b9dc3bc-6bdd-4f95-96d1-281f9d2e9768"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="114a5665-1123-42be-8d4a-97a76d7eb902"
            name="MessageOccurrenceSpecification58">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="878ec1e9-abcf-477d-8de0-0423f6ebc747"
            name="MessageOccurrenceSpecification57">
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="f4a78726-a65a-4e6e-8a03-7e77a6850143"
            name="MessageOccurrenceSpecification59">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="c4a70f3f-816f-4d2d-91f6-cc5adf5f3c92"
            name="MessageOccurrenceSpecification60">
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="2917886c-83c8-40a5-97fe-5260c60bff3c"
            name="ExecutionOccurrenceSpecification34">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="af8bf7ea-37b5-4cd5-9295-adf60023495a"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="4dfef78e-ac5b-440b-b52e-711519aaffdd"
            name="MessageOccurrenceSpecification39">
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="c2d15ea5-0f4d-4a30-9b85-d3a12cf07302"
            name="MessageOccurrenceSpecification40">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="f0375f1b-905a-4f25-ac0b-cc762bdcffdb"
            name="ExecutionOccurrenceSpecification22">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="c3dce236-9764-4ea3-bb33-876d83b74c28"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="f811e633-c380-46eb-b1e7-07a9754d0bd0"
            name="BehaviorExecutionSpecification12">
            <coveredLifelines>
              <lifelineMoniker
                Id="5d36673f-ce2a-4fb7-9dca-7630242abc82"
                LastKnownName="SingleThreadTimer"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="bd5981fc-9e36-46e5-88c4-67a85b556683"
                LastKnownName="ExecutionOccurrenceSpecification24"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="0fb6ab88-e022-40d1-bbad-b00bc6feb20a"
                LastKnownName="ExecutionOccurrenceSpecification23"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="48479e97-b5f3-4a12-9794-ea145e775a5f"
                LastKnownName="MessageOccurrenceSpecification42"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="0411b448-3763-433c-adfe-1683962a84c8"
                LastKnownName="MessageOccurrenceSpecification43"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="0fb6ab88-e022-40d1-bbad-b00bc6feb20a"
            name="ExecutionOccurrenceSpecification23">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="619be2fd-b6ab-42b3-87ea-47e76a48d095"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="5d36673f-ce2a-4fb7-9dca-7630242abc82"
                LastKnownName="SingleThreadTimer"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="3481d0a1-cde9-446e-a0c8-6e0b94ce8b93"
            name="MessageOccurrenceSpecification41">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="48479e97-b5f3-4a12-9794-ea145e775a5f"
            name="MessageOccurrenceSpecification42">
            <covered>
              <lifelineMoniker
                Id="5d36673f-ce2a-4fb7-9dca-7630242abc82"
                LastKnownName="SingleThreadTimer"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="0411b448-3763-433c-adfe-1683962a84c8"
            name="MessageOccurrenceSpecification43">
            <covered>
              <lifelineMoniker
                Id="5d36673f-ce2a-4fb7-9dca-7630242abc82"
                LastKnownName="SingleThreadTimer"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="6075a1b8-2880-4fff-a0ee-35073ab6e106"
            name="MessageOccurrenceSpecification44">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="bd5981fc-9e36-46e5-88c4-67a85b556683"
            name="ExecutionOccurrenceSpecification24">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="eb2c96d2-4280-4a4b-bb70-7f7f067cbe0d"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="5d36673f-ce2a-4fb7-9dca-7630242abc82"
                LastKnownName="SingleThreadTimer"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="83456584-1366-4504-9087-f7b85a2a577d"
            name="OperandOccurrenceSpecification4">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="9a230c52-ce60-48b7-9a66-b51f2600db89"
            name="OperandOccurrenceSpecification2">
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="a19401c9-b7e1-4da8-ab2e-2912b871ab73"
            name="OperandOccurrenceSpecification6">
            <covered>
              <lifelineMoniker
                Id="5d36673f-ce2a-4fb7-9dca-7630242abc82"
                LastKnownName="SingleThreadTimer"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="efdf61b6-6617-49aa-b390-d94b0386e476"
            name="MessageOccurrenceSpecification36">
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="c32a7b1b-5b67-4dcb-8b79-db3941fe4b84"
            name="MessageOccurrenceSpecification35">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="64fe7467-1093-48c6-81b9-0562fca23244"
            name="ExecutionOccurrenceSpecification20">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="64c57bf9-0210-45fc-ad7d-5e0843d4d58a"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="d6b205fc-2593-442a-8750-f29298f1e29e"
            name="MessageOccurrenceSpecification27">
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="e1d4f946-938b-4d80-9e31-f7d65df0d635"
            name="MessageOccurrenceSpecification28">
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="0e7d9e15-9f57-4ccb-a605-b7cfa9ff22a7"
            name="ExecutionOccurrenceSpecification16">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="5ea60b11-72fb-4d19-9062-0dfaec551e41"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="0a0c0b8e-d3a3-43ee-8f18-9b5e6d6022c7"
            name="ExecutionOccurrenceSpecification12">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="bb736861-03a7-496c-b03a-47b0c62469f2"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
        </fragments>
        <lifelines>
          <lifeline
            Id="168db48c-61d4-4e47-9b96-530868c97e2a"
            name="Controller"
            isActor="false"
            lifelineDisplayName="Controller">
            <topLevelOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="ebc065c4-14a5-4f06-b1d4-1cf7dbaabd53"
                LastKnownName="MessageOccurrenceSpecification21"
                LastKnownLocation="models-camp-us2.uml" />
            </topLevelOccurrences>
          </lifeline>
          <lifeline
            Id="5d36673f-ce2a-4fb7-9dca-7630242abc82"
            name="SingleThreadTimer"
            isActor="false"
            lifelineDisplayName="SingleThreadTimer">
            <topLevelOccurrences>
              <executionOccurrenceSpecificationMoniker
                Id="9c030e8d-990c-4f13-9ae0-5fe62358a444"
                LastKnownName="ExecutionOccurrenceSpecification25"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="37a4e05b-bd65-4d06-8c35-b6303296e17d"
                LastKnownName="ExecutionOccurrenceSpecification26"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="a35efec6-a053-479f-95ae-4e9820efb05a"
                LastKnownName="OperandOccurrenceSpecification5"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="0fb6ab88-e022-40d1-bbad-b00bc6feb20a"
                LastKnownName="ExecutionOccurrenceSpecification23"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="bd5981fc-9e36-46e5-88c4-67a85b556683"
                LastKnownName="ExecutionOccurrenceSpecification24"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="a19401c9-b7e1-4da8-ab2e-2912b871ab73"
                LastKnownName="OperandOccurrenceSpecification6"
                LastKnownLocation="models-camp-us2.uml" />
            </topLevelOccurrences>
          </lifeline>
          <lifeline
            Id="aa5ae247-25e4-4baf-92b2-a2ddad4bdbc0"
            name=": AbstractInput"
            isActor="false"
            lifelineDisplayName=": AbstractInput">
            <topLevelOccurrences>
              <executionOccurrenceSpecificationMoniker
                Id="b3ee0a94-f9ab-434b-beca-dbd1559a9077"
                LastKnownName="ExecutionOccurrenceSpecification11"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="0a0c0b8e-d3a3-43ee-8f18-9b5e6d6022c7"
                LastKnownName="ExecutionOccurrenceSpecification12"
                LastKnownLocation="models-camp-us2.uml" />
            </topLevelOccurrences>
          </lifeline>
          <lifeline
            Id="7cdb6295-7560-4b35-a0a0-a35821f1b5f6"
            name=": InputDevice:AbstractInput"
            isActor="false"
            lifelineDisplayName=": InputDevice:AbstractInput">
            <topLevelOccurrences>
              <executionOccurrenceSpecificationMoniker
                Id="4733b52a-5a45-442a-a3ca-11b1b4241e91"
                LastKnownName="ExecutionOccurrenceSpecification15"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="0e7d9e15-9f57-4ccb-a605-b7cfa9ff22a7"
                LastKnownName="ExecutionOccurrenceSpecification16"
                LastKnownLocation="models-camp-us2.uml" />
            </topLevelOccurrences>
          </lifeline>
        </lifelines>
        <messages>
          <message
            Id="8e380548-aa76-400b-813e-7552814c2793"
            name="start (in new thread)"
            messageKind="Complete"
            messageSort="AsynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="ebc065c4-14a5-4f06-b1d4-1cf7dbaabd53"
                LastKnownName="MessageOccurrenceSpecification21"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="96ea4439-7568-4e98-b000-17b5c24a3b82"
                LastKnownName="MessageOccurrenceSpecification22"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="e347ecc7-9c6b-43e1-820d-a6a9b6f03d0a"
            name="setRunning(true)"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="true">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="4ba7cfaf-24fa-4b6b-ac1e-ceaad340f9d2"
                LastKnownName="MessageOccurrenceSpecification23"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="97bbcae6-ba52-45a1-9faf-d19d0debbcf8"
                LastKnownName="MessageOccurrenceSpecification24"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="cdc92a73-0417-4ebb-b3fd-3ad888a9512a"
            name="startAcquisition()"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="1aaa6cd8-2661-4775-a0b3-b1a2619f5290"
                LastKnownName="MessageOccurrenceSpecification25"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="b1e08dad-2ed7-4678-bce8-232732508b56"
                LastKnownName="MessageOccurrenceSpecification26"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="7cf83a9c-b803-47c8-be19-3c0b1cdc09cd"
            name="setUpTimer()"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="eeb5eb54-f25a-4fb9-b09d-2119f9442d35"
                LastKnownName="MessageOccurrenceSpecification29"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="8f05553c-d437-4ffc-bf4f-d981eca82993"
                LastKnownName="MessageOccurrenceSpecification30"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="42ff092a-e071-4d41-ab48-873a86b276bd"
            name="setFrequency()"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="2aa80dbb-7e9d-4304-b478-a0bb5dd35001"
                LastKnownName="MessageOccurrenceSpecification45"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="3032c8ae-35e5-41bf-a6d7-05b56ec3d5b8"
                LastKnownName="MessageOccurrenceSpecification46"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="b5c2b36b-4000-4d81-956a-ca13ca6fcb2b"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="c4df4451-6bfa-4e43-8159-e064a66db019"
                LastKnownName="MessageOccurrenceSpecification47"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="c6b6c6f6-bfe4-4c12-ab0b-07c40fe31eb8"
                LastKnownName="MessageOccurrenceSpecification48"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="af35f384-4e69-431f-95c6-342b0b4f3a94"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="ceffa0d1-a141-452b-aa52-85d30f1fdbab"
                LastKnownName="MessageOccurrenceSpecification31"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="cbaac0e0-ba93-4606-8a69-e7581994f410"
                LastKnownName="MessageOccurrenceSpecification32"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="2ea69c7b-e400-49ad-95bc-42ca6059eab9"
            name="timerLoop()"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="4ec02fda-802a-42fc-9b0c-441fa6547a70"
                LastKnownName="MessageOccurrenceSpecification33"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="d72b93cd-1642-4e9b-b38a-810f53518803"
                LastKnownName="MessageOccurrenceSpecification34"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="726c5e18-7b9e-4ba9-a5ab-9d7c77bba426"
            name="timerCallback()"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="c00e922b-cc50-433a-9325-65fc63ea1a1b"
                LastKnownName="MessageOccurrenceSpecification37"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="6718fdf4-2db3-43e6-a68f-9cb92066e533"
                LastKnownName="MessageOccurrenceSpecification38"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="423cd1ef-b5d6-4122-87ad-5eb5f2868e47"
            name="generate data object"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="true">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="02e518d4-578a-4616-9eaa-6cb4c8126f6b"
                LastKnownName="MessageOccurrenceSpecification55"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="009e13d3-acdb-4e90-af5f-a24b478e0069"
                LastKnownName="MessageOccurrenceSpecification56"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="218cab37-273c-4d63-b351-32b1ca96f605"
            name="addData()"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="878ec1e9-abcf-477d-8de0-0423f6ebc747"
                LastKnownName="MessageOccurrenceSpecification57"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="114a5665-1123-42be-8d4a-97a76d7eb902"
                LastKnownName="MessageOccurrenceSpecification58"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="ae6d55c3-44a8-421e-8e23-4620b6168864"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="f4a78726-a65a-4e6e-8a03-7e77a6850143"
                LastKnownName="MessageOccurrenceSpecification59"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="c4a70f3f-816f-4d2d-91f6-cc5adf5f3c92"
                LastKnownName="MessageOccurrenceSpecification60"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="8114559b-c0f0-467f-831f-20e3b1cacdd9"
            name="bool continueTimer"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="4dfef78e-ac5b-440b-b52e-711519aaffdd"
                LastKnownName="MessageOccurrenceSpecification39"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="c2d15ea5-0f4d-4a30-9b85-d3a12cf07302"
                LastKnownName="MessageOccurrenceSpecification40"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="41eb34b0-5ba5-48d5-9ec5-d9361fea5d18"
            name="sleepUntilNextSlot()"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="3481d0a1-cde9-446e-a0c8-6e0b94ce8b93"
                LastKnownName="MessageOccurrenceSpecification41"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="48479e97-b5f3-4a12-9794-ea145e775a5f"
                LastKnownName="MessageOccurrenceSpecification42"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="13ead158-348a-4251-95ba-ee0db2898765"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="0411b448-3763-433c-adfe-1683962a84c8"
                LastKnownName="MessageOccurrenceSpecification43"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="6075a1b8-2880-4fff-a0ee-35073ab6e106"
                LastKnownName="MessageOccurrenceSpecification44"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="ecaae67b-da18-4e00-852d-c83855879163"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="c32a7b1b-5b67-4dcb-8b79-db3941fe4b84"
                LastKnownName="MessageOccurrenceSpecification35"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="efdf61b6-6617-49aa-b390-d94b0386e476"
                LastKnownName="MessageOccurrenceSpecification36"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="10a4a399-1445-40af-a86f-4415bdd1d774"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="d6b205fc-2593-442a-8750-f29298f1e29e"
                LastKnownName="MessageOccurrenceSpecification27"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="e1d4f946-938b-4d80-9e31-f7d65df0d635"
                LastKnownName="MessageOccurrenceSpecification28"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
        </messages>
      </interaction>
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="a39a0443-a177-4312-9e3c-778137907205"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="bb736861-03a7-496c-b03a-47b0c62469f2"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="cc747941-67a8-46e2-8711-658fc8ff02d0"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="4bdbe986-afa4-44b5-a827-d0fba2a84b05"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="1b108df5-a7e9-478c-8754-7bbe65477239"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="5ea60b11-72fb-4d19-9062-0dfaec551e41"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="0086621b-a734-4d75-983e-e3f96ab0a391"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="62665baf-e7f1-4456-a297-299fad6d3944"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="2bdeb1b2-adba-4136-a9f0-7f574cc40c02"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="64c57bf9-0210-45fc-ad7d-5e0843d4d58a"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="6b544cda-6d05-4492-9a1b-7829ae5bbfec"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="c3dce236-9764-4ea3-bb33-876d83b74c28"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="619be2fd-b6ab-42b3-87ea-47e76a48d095"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="eb2c96d2-4280-4a4b-bb70-7f7f067cbe0d"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="31aae22a-b1ca-4037-ac4c-92d08a732beb"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="484cec9b-c203-4a6d-a530-152c20679144"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <interaction
        Id="19801279-0159-4299-a557-faf88e273026"
        name="SequenceInputStreamCallback"
        collapseFragmentsFlag="false"
        isActiveClass="false"
        isAbstract="false"
        isLeaf="false"
        isReentrant="false">
        <fragments>
          <behaviorExecutionSpecification
            Id="f69e2f7b-88aa-44fb-97aa-d461099c7d60"
            name="BehaviorExecutionSpecification6">
            <coveredLifelines>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="6988a07e-e1b8-4b22-86f2-670da9bacb68"
                LastKnownName="ExecutionOccurrenceSpecification12"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="4a7ed2ef-3770-4e51-a646-0dbc6e55beec"
                LastKnownName="ExecutionOccurrenceSpecification11"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="066eb57b-220f-42c4-87ce-bd8f33174a08"
                LastKnownName="MessageOccurrenceSpecification22"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="d83f43ff-ee0e-42eb-b0e4-20e31999df91"
                LastKnownName="MessageOccurrenceSpecification23"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="d9556d91-0ab0-4cb3-90d1-5f28e85bb972"
                LastKnownName="ExecutionOccurrenceSpecification13"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="d27f7799-a029-43e2-99b9-5aaa511b48eb"
                LastKnownName="ExecutionOccurrenceSpecification14"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="c04c08d3-4cc7-4efe-8f99-df722d89b54e"
                LastKnownName="MessageOccurrenceSpecification25"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="b159f566-abf7-4080-8453-0079508fa8c1"
                LastKnownName="MessageOccurrenceSpecification28"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="4a7ed2ef-3770-4e51-a646-0dbc6e55beec"
            name="ExecutionOccurrenceSpecification11">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="adfb1da3-e9a9-4a9b-aa21-952914013e3c"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="b5ebaa76-deac-4615-b79a-aad2a9d7b0b7"
            name="MessageOccurrenceSpecification21">
            <covered>
              <lifelineMoniker
                Id="905e99ed-5eb8-4314-a476-77ed1248e72e"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="066eb57b-220f-42c4-87ce-bd8f33174a08"
            name="MessageOccurrenceSpecification22">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="d83f43ff-ee0e-42eb-b0e4-20e31999df91"
            name="MessageOccurrenceSpecification23">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="0d1e11f6-b47a-4c41-848a-f435cc48d49f"
            name="BehaviorExecutionSpecification7">
            <coveredLifelines>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="d27f7799-a029-43e2-99b9-5aaa511b48eb"
                LastKnownName="ExecutionOccurrenceSpecification14"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="d9556d91-0ab0-4cb3-90d1-5f28e85bb972"
                LastKnownName="ExecutionOccurrenceSpecification13"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="6cac31e5-98f2-4fa2-babd-275591b38612"
                LastKnownName="MessageOccurrenceSpecification24"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="d9556d91-0ab0-4cb3-90d1-5f28e85bb972"
            name="ExecutionOccurrenceSpecification13">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="c1433b9f-47e8-4e68-a484-8ab2df4b0b66"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="6cac31e5-98f2-4fa2-babd-275591b38612"
            name="MessageOccurrenceSpecification24">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="d27f7799-a029-43e2-99b9-5aaa511b48eb"
            name="ExecutionOccurrenceSpecification14">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="d649eac8-475e-4194-9c97-3ec6308b955f"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="02087272-a1a2-4f80-bbb4-b0caf0c8fa11"
            name="BehaviorExecutionSpecification8">
            <coveredLifelines>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="c3252457-a4b8-4f43-9f80-540bf943b3e2"
                LastKnownName="ExecutionOccurrenceSpecification16"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="33d937ef-93d0-49b9-a899-481b388c4b0c"
                LastKnownName="ExecutionOccurrenceSpecification15"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="0fcc468b-dbb0-475c-8be0-a08f05420bf2"
                LastKnownName="MessageOccurrenceSpecification26"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="438a5457-0475-41c8-8d97-ad5b7c91e9aa"
                LastKnownName="MessageOccurrenceSpecification45"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="d38cf595-7357-4928-b201-c73db168d72c"
                LastKnownName="MessageOccurrenceSpecification48"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="accd6930-ac21-4376-8fb6-30dd6d3c1c0f"
                LastKnownName="MessageOccurrenceSpecification27"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="33d937ef-93d0-49b9-a899-481b388c4b0c"
            name="ExecutionOccurrenceSpecification15">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="59a61876-1bd5-4182-8858-f7bc2caef407"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="0fcc468b-dbb0-475c-8be0-a08f05420bf2"
            name="MessageOccurrenceSpecification26">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="c04c08d3-4cc7-4efe-8f99-df722d89b54e"
            name="MessageOccurrenceSpecification25">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="00e1fb5d-1436-4800-9881-2091b7759525"
            name="BehaviorExecutionSpecification13">
            <coveredLifelines>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="b6988e68-980c-40c2-8546-44c15fa6b512"
                LastKnownName="ExecutionOccurrenceSpecification26"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="0f2327fc-143f-4684-9ae2-840f9ae600a2"
                LastKnownName="ExecutionOccurrenceSpecification25"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="302cfccd-3ef4-48a1-bddc-d9b96a85940b"
                LastKnownName="MessageOccurrenceSpecification46"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="5070cb35-cb95-41a1-a987-3aa7aa46e593"
                LastKnownName="MessageOccurrenceSpecification47"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="0f2327fc-143f-4684-9ae2-840f9ae600a2"
            name="ExecutionOccurrenceSpecification25">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="23ede670-8fd4-44f7-abcd-4ddba139a261"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="302cfccd-3ef4-48a1-bddc-d9b96a85940b"
            name="MessageOccurrenceSpecification46">
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="438a5457-0475-41c8-8d97-ad5b7c91e9aa"
            name="MessageOccurrenceSpecification45">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="5070cb35-cb95-41a1-a987-3aa7aa46e593"
            name="MessageOccurrenceSpecification47">
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="d38cf595-7357-4928-b201-c73db168d72c"
            name="MessageOccurrenceSpecification48">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="b6988e68-980c-40c2-8546-44c15fa6b512"
            name="ExecutionOccurrenceSpecification26">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="d7edc92d-c5c0-4c13-92a4-a58ea9720bd4"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="b159f566-abf7-4080-8453-0079508fa8c1"
            name="MessageOccurrenceSpecification28">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="accd6930-ac21-4376-8fb6-30dd6d3c1c0f"
            name="MessageOccurrenceSpecification27">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="c3252457-a4b8-4f43-9f80-540bf943b3e2"
            name="ExecutionOccurrenceSpecification16">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="21560ab3-7788-4ec7-b382-4b351cad092e"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="6988a07e-e1b8-4b22-86f2-670da9bacb68"
            name="ExecutionOccurrenceSpecification12">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="940420c5-df65-4ae8-8d94-af62cbecfa5b"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <combinedFragment
            Id="68dded63-5910-43e9-8a49-1e545a43baca"
            name="CombinedFragment1"
            interactionOperator="Loop">
            <coveredLifelines>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
              <lifelineMoniker
                Id="905e99ed-5eb8-4314-a476-77ed1248e72e"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <operands>
              <interactionOperand
                Id="a0150c94-8ff0-4fbc-9903-842728cdd39a"
                name="InteractionOperand1">
                <coveredLifelines>
                  <lifelineMoniker
                    Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                    LastKnownName=": AbstractInput"
                    LastKnownLocation="models-camp-us2.uml" />
                  <lifelineMoniker
                    Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                    LastKnownName=": InputDevice:AbstractInput"
                    LastKnownLocation="models-camp-us2.uml" />
                  <lifelineMoniker
                    Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                    LastKnownName="DeviceAPI"
                    LastKnownLocation="models-camp-us2.uml" />
                  <lifelineMoniker
                    Id="905e99ed-5eb8-4314-a476-77ed1248e72e"
                    LastKnownName="Controller"
                    LastKnownLocation="models-camp-us2.uml" />
                </coveredLifelines>
                <fragments>
                  <combinedFragment
                    Id="b7e941bc-e5db-452e-bc6c-ab0fd8f113bd"
                    name="CombinedFragment1"
                    interactionOperator="Alt">
                    <coveredLifelines>
                      <lifelineMoniker
                        Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                        LastKnownName=": InputDevice:AbstractInput"
                        LastKnownLocation="models-camp-us2.uml" />
                      <lifelineMoniker
                        Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                        LastKnownName="DeviceAPI"
                        LastKnownLocation="models-camp-us2.uml" />
                      <lifelineMoniker
                        Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                        LastKnownName=": AbstractInput"
                        LastKnownLocation="models-camp-us2.uml" />
                      <lifelineMoniker
                        Id="905e99ed-5eb8-4314-a476-77ed1248e72e"
                        LastKnownName="Controller"
                        LastKnownLocation="models-camp-us2.uml" />
                    </coveredLifelines>
                    <operands>
                      <interactionOperand
                        Id="5948bce1-bf76-49ba-bcaa-783fb7eed89e"
                        name="InteractionOperand1">
                        <coveredLifelines>
                          <lifelineMoniker
                            Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                            LastKnownName=": InputDevice:AbstractInput"
                            LastKnownLocation="models-camp-us2.uml" />
                          <lifelineMoniker
                            Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                            LastKnownName="DeviceAPI"
                            LastKnownLocation="models-camp-us2.uml" />
                          <lifelineMoniker
                            Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                            LastKnownName=": AbstractInput"
                            LastKnownLocation="models-camp-us2.uml" />
                          <lifelineMoniker
                            Id="905e99ed-5eb8-4314-a476-77ed1248e72e"
                            LastKnownName="Controller"
                            LastKnownLocation="models-camp-us2.uml" />
                        </coveredLifelines>
                        <guard>
                          <interactionConstraint
                            Id="391af567-2309-4f27-a920-682a3b97a202"
                            guardText="exclusiveness protected across different threads (mutex)" />
                        </guard>
                        <operandOccurrenceSpecifications>
                          <operandOccurrenceSpecificationMoniker
                            Id="b8b0f3ce-c493-4525-94d6-0967582f8cfb"
                            LastKnownName="OperandOccurrenceSpecification13"
                            LastKnownLocation="models-camp-us2.uml" />
                          <operandOccurrenceSpecificationMoniker
                            Id="35bab701-25b1-4974-a059-41344f2a778c"
                            LastKnownName="OperandOccurrenceSpecification14"
                            LastKnownLocation="models-camp-us2.uml" />
                          <operandOccurrenceSpecificationMoniker
                            Id="c430ed2a-f8db-46ab-a7b5-e8df0ff39b6d"
                            LastKnownName="OperandOccurrenceSpecification15"
                            LastKnownLocation="models-camp-us2.uml" />
                          <operandOccurrenceSpecificationMoniker
                            Id="2234f129-ef43-458d-8815-102d0408170c"
                            LastKnownName="OperandOccurrenceSpecification16"
                            LastKnownLocation="models-camp-us2.uml" />
                          <operandOccurrenceSpecificationMoniker
                            Id="8e03ef36-2643-4c01-9c96-64a5aa71cd53"
                            LastKnownName="OperandOccurrenceSpecification17"
                            LastKnownLocation="models-camp-us2.uml" />
                          <operandOccurrenceSpecificationMoniker
                            Id="b3958325-d109-4c48-a906-bdf8d0a04f97"
                            LastKnownName="OperandOccurrenceSpecification18"
                            LastKnownLocation="models-camp-us2.uml" />
                          <operandOccurrenceSpecificationMoniker
                            Id="8d57b6f0-a031-4fa1-8aa0-63b0362d9a71"
                            LastKnownName="OperandOccurrenceSpecification25"
                            LastKnownLocation="models-camp-us2.uml" />
                          <operandOccurrenceSpecificationMoniker
                            Id="c1b7ed32-0463-4be3-91a5-31ddf53fbba0"
                            LastKnownName="OperandOccurrenceSpecification26"
                            LastKnownLocation="models-camp-us2.uml" />
                        </operandOccurrenceSpecifications>
                      </interactionOperand>
                      <interactionOperand
                        Id="e192504e-b740-4fb1-9fae-52be3ac9ae2c"
                        name="InteractionOperand2">
                        <coveredLifelines>
                          <lifelineMoniker
                            Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                            LastKnownName=": InputDevice:AbstractInput"
                            LastKnownLocation="models-camp-us2.uml" />
                          <lifelineMoniker
                            Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                            LastKnownName="DeviceAPI"
                            LastKnownLocation="models-camp-us2.uml" />
                          <lifelineMoniker
                            Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                            LastKnownName=": AbstractInput"
                            LastKnownLocation="models-camp-us2.uml" />
                          <lifelineMoniker
                            Id="905e99ed-5eb8-4314-a476-77ed1248e72e"
                            LastKnownName="Controller"
                            LastKnownLocation="models-camp-us2.uml" />
                        </coveredLifelines>
                        <guard>
                          <interactionConstraint
                            Id="f679f784-5fd7-49e7-8144-43b70db3877a" />
                        </guard>
                        <operandOccurrenceSpecifications>
                          <operandOccurrenceSpecificationMoniker
                            Id="addbf177-74e8-491d-bb7d-6a5b056808a3"
                            LastKnownName="OperandOccurrenceSpecification19"
                            LastKnownLocation="models-camp-us2.uml" />
                          <operandOccurrenceSpecificationMoniker
                            Id="7af295d0-f2cd-44de-8b29-19496afa490c"
                            LastKnownName="OperandOccurrenceSpecification20"
                            LastKnownLocation="models-camp-us2.uml" />
                          <operandOccurrenceSpecificationMoniker
                            Id="249ff49f-3682-4572-9d00-6f987d744724"
                            LastKnownName="OperandOccurrenceSpecification21"
                            LastKnownLocation="models-camp-us2.uml" />
                          <operandOccurrenceSpecificationMoniker
                            Id="889e6dff-6ccb-4431-b980-6ced82cbf39f"
                            LastKnownName="OperandOccurrenceSpecification22"
                            LastKnownLocation="models-camp-us2.uml" />
                          <operandOccurrenceSpecificationMoniker
                            Id="0681c4e1-c0c3-4724-b53c-60fbec526451"
                            LastKnownName="OperandOccurrenceSpecification23"
                            LastKnownLocation="models-camp-us2.uml" />
                          <operandOccurrenceSpecificationMoniker
                            Id="4b253f1a-238f-4b7b-943d-488e7498a032"
                            LastKnownName="OperandOccurrenceSpecification24"
                            LastKnownLocation="models-camp-us2.uml" />
                          <operandOccurrenceSpecificationMoniker
                            Id="e9c9bb27-eae6-4068-abba-da364a18e519"
                            LastKnownName="OperandOccurrenceSpecification27"
                            LastKnownLocation="models-camp-us2.uml" />
                          <operandOccurrenceSpecificationMoniker
                            Id="58df0eda-8d12-4051-a6e5-57a0179a98b8"
                            LastKnownName="OperandOccurrenceSpecification28"
                            LastKnownLocation="models-camp-us2.uml" />
                        </operandOccurrenceSpecifications>
                      </interactionOperand>
                    </operands>
                  </combinedFragment>
                </fragments>
                <guard>
                  <interactionConstraint
                    Id="1b661493-59ca-4689-89dc-c86f5bbcb9ac">
                    <maxInt>
                      <literalString
                        Id="0b41b40b-e2c3-4049-9410-fa4fc6538cba"
                        name="LiteralString1" />
                    </maxInt>
                    <minInt>
                      <literalString
                        Id="81c6ea19-3def-4f2b-ad7f-1324a80fc7bb"
                        name="LiteralString2" />
                    </minInt>
                  </interactionConstraint>
                </guard>
                <operandOccurrenceSpecifications>
                  <operandOccurrenceSpecificationMoniker
                    Id="2e269abd-2208-42ea-b8af-863f02d0704a"
                    LastKnownName="OperandOccurrenceSpecification7"
                    LastKnownLocation="models-camp-us2.uml" />
                  <operandOccurrenceSpecificationMoniker
                    Id="8de9d2a2-90b2-4055-9ffb-fe5638c0c7c1"
                    LastKnownName="OperandOccurrenceSpecification8"
                    LastKnownLocation="models-camp-us2.uml" />
                  <operandOccurrenceSpecificationMoniker
                    Id="a9d248fd-8bfa-47d8-9901-7c83f1fe7312"
                    LastKnownName="OperandOccurrenceSpecification9"
                    LastKnownLocation="models-camp-us2.uml" />
                  <operandOccurrenceSpecificationMoniker
                    Id="644bcea7-14a1-4ab4-ab6c-6955410491e1"
                    LastKnownName="OperandOccurrenceSpecification10"
                    LastKnownLocation="models-camp-us2.uml" />
                  <operandOccurrenceSpecificationMoniker
                    Id="1b5bee7f-7e00-495b-bf58-f4f92b281b2b"
                    LastKnownName="OperandOccurrenceSpecification11"
                    LastKnownLocation="models-camp-us2.uml" />
                  <operandOccurrenceSpecificationMoniker
                    Id="41fbe7f7-693a-41b5-8544-f90f316c5350"
                    LastKnownName="OperandOccurrenceSpecification12"
                    LastKnownLocation="models-camp-us2.uml" />
                  <operandOccurrenceSpecificationMoniker
                    Id="9d2d450b-3376-490f-adf5-59e9351e58da"
                    LastKnownName="OperandOccurrenceSpecification29"
                    LastKnownLocation="models-camp-us2.uml" />
                  <operandOccurrenceSpecificationMoniker
                    Id="34111cd1-5050-4218-8b5f-347db698ec61"
                    LastKnownName="OperandOccurrenceSpecification30"
                    LastKnownLocation="models-camp-us2.uml" />
                </operandOccurrenceSpecifications>
              </interactionOperand>
            </operands>
          </combinedFragment>
          <operandOccurrenceSpecification
            Id="1b5bee7f-7e00-495b-bf58-f4f92b281b2b"
            name="OperandOccurrenceSpecification11">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="9d2d450b-3376-490f-adf5-59e9351e58da"
            name="OperandOccurrenceSpecification29">
            <covered>
              <lifelineMoniker
                Id="905e99ed-5eb8-4314-a476-77ed1248e72e"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="a9d248fd-8bfa-47d8-9901-7c83f1fe7312"
            name="OperandOccurrenceSpecification9">
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="2e269abd-2208-42ea-b8af-863f02d0704a"
            name="OperandOccurrenceSpecification7">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="b8b0f3ce-c493-4525-94d6-0967582f8cfb"
            name="OperandOccurrenceSpecification13">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="8e03ef36-2643-4c01-9c96-64a5aa71cd53"
            name="OperandOccurrenceSpecification17">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="8d57b6f0-a031-4fa1-8aa0-63b0362d9a71"
            name="OperandOccurrenceSpecification25">
            <covered>
              <lifelineMoniker
                Id="905e99ed-5eb8-4314-a476-77ed1248e72e"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="c430ed2a-f8db-46ab-a7b5-e8df0ff39b6d"
            name="OperandOccurrenceSpecification15">
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="dc92668d-069d-40ed-8603-a5a85e4ce902"
            name="BehaviorExecutionSpecification15">
            <coveredLifelines>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="67d0b923-e3bc-41cd-b147-dd2b0c79c3b7"
                LastKnownName="ExecutionOccurrenceSpecification30"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="85da0ffe-6d83-4611-b8a5-8a6da9490d37"
                LastKnownName="ExecutionOccurrenceSpecification29"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="486410f0-7ee8-45dc-acb0-ba209de8027a"
                LastKnownName="MessageOccurrenceSpecification52"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="6a1ddf6e-9df1-4b79-859a-fefb852632e1"
                LastKnownName="MessageOccurrenceSpecification55"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="499f289d-0706-4aed-bf63-1803f0830889"
                LastKnownName="ExecutionOccurrenceSpecification31"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="c495dc22-b537-451f-8041-93151de5fddd"
                LastKnownName="ExecutionOccurrenceSpecification32"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="3e2a6e09-4241-4a12-8b10-a6c83ee3895c"
                LastKnownName="MessageOccurrenceSpecification57"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="ce4720f3-4bcd-470d-a2e3-559d967e0ec6"
                LastKnownName="MessageOccurrenceSpecification60"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="8b339fdc-34a8-47e2-903a-14cc4923073e"
                LastKnownName="MessageOccurrenceSpecification53"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="85da0ffe-6d83-4611-b8a5-8a6da9490d37"
            name="ExecutionOccurrenceSpecification29">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="ac558aa7-059d-42c0-9eb2-40be67b78f44"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="2078f5ff-bbc9-464b-b5d3-c7e2d0393e86"
            name="MessageOccurrenceSpecification51">
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="486410f0-7ee8-45dc-acb0-ba209de8027a"
            name="MessageOccurrenceSpecification52">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="6a1ddf6e-9df1-4b79-859a-fefb852632e1"
            name="MessageOccurrenceSpecification55">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="b95f0484-1d63-4d79-80c2-8d029d76ddd0"
            name="BehaviorExecutionSpecification16">
            <coveredLifelines>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="c495dc22-b537-451f-8041-93151de5fddd"
                LastKnownName="ExecutionOccurrenceSpecification32"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="499f289d-0706-4aed-bf63-1803f0830889"
                LastKnownName="ExecutionOccurrenceSpecification31"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="7199d3f9-1818-45f3-9390-c8f956fb5ea7"
                LastKnownName="MessageOccurrenceSpecification56"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="499f289d-0706-4aed-bf63-1803f0830889"
            name="ExecutionOccurrenceSpecification31">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="1dc5ef5a-c372-4aaa-b6ca-fb770e5fa1c0"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="7199d3f9-1818-45f3-9390-c8f956fb5ea7"
            name="MessageOccurrenceSpecification56">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="c495dc22-b537-451f-8041-93151de5fddd"
            name="ExecutionOccurrenceSpecification32">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="1fb374a9-4f5c-4520-9183-11ae5e65a12c"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="a8326e7c-1b37-4040-9125-19976219c772"
            name="BehaviorExecutionSpecification17">
            <coveredLifelines>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="3c844b2c-4806-4591-bd84-72ecb3f7990c"
                LastKnownName="ExecutionOccurrenceSpecification34"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="bb0d67c9-19b1-4ecd-9529-07613c31f234"
                LastKnownName="ExecutionOccurrenceSpecification33"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="99bcdd99-9acf-4022-8bfb-b0e113882cde"
                LastKnownName="MessageOccurrenceSpecification58"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="c6250cc4-63d6-4548-8d21-afcce334d9a9"
                LastKnownName="MessageOccurrenceSpecification59"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="bb0d67c9-19b1-4ecd-9529-07613c31f234"
            name="ExecutionOccurrenceSpecification33">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="5b472ce4-8030-48fc-9376-daab67543e4b"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="99bcdd99-9acf-4022-8bfb-b0e113882cde"
            name="MessageOccurrenceSpecification58">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="3e2a6e09-4241-4a12-8b10-a6c83ee3895c"
            name="MessageOccurrenceSpecification57">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="c6250cc4-63d6-4548-8d21-afcce334d9a9"
            name="MessageOccurrenceSpecification59">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="ce4720f3-4bcd-470d-a2e3-559d967e0ec6"
            name="MessageOccurrenceSpecification60">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="3c844b2c-4806-4591-bd84-72ecb3f7990c"
            name="ExecutionOccurrenceSpecification34">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="e7a9b9cf-5803-450f-a887-90b566424584"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="c7cd8097-ce2d-4c96-89c6-ceee6bdd8db7"
            name="MessageOccurrenceSpecification54">
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="8b339fdc-34a8-47e2-903a-14cc4923073e"
            name="MessageOccurrenceSpecification53">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="67d0b923-e3bc-41cd-b147-dd2b0c79c3b7"
            name="ExecutionOccurrenceSpecification30">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="d993c360-abe9-45d6-9818-e8f27d93a347"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="35bab701-25b1-4974-a059-41344f2a778c"
            name="OperandOccurrenceSpecification14">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="b3958325-d109-4c48-a906-bdf8d0a04f97"
            name="OperandOccurrenceSpecification18">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="c1b7ed32-0463-4be3-91a5-31ddf53fbba0"
            name="OperandOccurrenceSpecification26">
            <covered>
              <lifelineMoniker
                Id="905e99ed-5eb8-4314-a476-77ed1248e72e"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="2234f129-ef43-458d-8815-102d0408170c"
            name="OperandOccurrenceSpecification16">
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="0681c4e1-c0c3-4724-b53c-60fbec526451"
            name="OperandOccurrenceSpecification23">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="e9c9bb27-eae6-4068-abba-da364a18e519"
            name="OperandOccurrenceSpecification27">
            <covered>
              <lifelineMoniker
                Id="905e99ed-5eb8-4314-a476-77ed1248e72e"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="addbf177-74e8-491d-bb7d-6a5b056808a3"
            name="OperandOccurrenceSpecification19">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="249ff49f-3682-4572-9d00-6f987d744724"
            name="OperandOccurrenceSpecification21">
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="0e1ce110-c578-4b3d-bb70-6a3d572a5011"
            name="BehaviorExecutionSpecification19">
            <coveredLifelines>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="b23c66fc-afab-493c-a071-65ec61b47428"
                LastKnownName="ExecutionOccurrenceSpecification38"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="d6a858c2-fa35-42a4-a292-cf6f48f5f000"
                LastKnownName="ExecutionOccurrenceSpecification37"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="596e8bdd-8e43-4976-9d8b-479d43582cd3"
                LastKnownName="MessageOccurrenceSpecification66"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="cc6bbac6-6233-486f-89da-4c92f33d5088"
                LastKnownName="MessageOccurrenceSpecification69"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="433ac68a-1a54-404b-a20d-130d5030078c"
                LastKnownName="MessageOccurrenceSpecification72"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="f20b11d0-b7ae-4e94-8ce8-37eb729221f9"
                LastKnownName="MessageOccurrenceSpecification67"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="d6a858c2-fa35-42a4-a292-cf6f48f5f000"
            name="ExecutionOccurrenceSpecification37">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="e4ae2b91-29c9-4003-8501-9a9844d5fe32"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="596e8bdd-8e43-4976-9d8b-479d43582cd3"
            name="MessageOccurrenceSpecification66">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="1eecbfc6-2d95-4f2a-865e-a2d790ac765b"
            name="MessageOccurrenceSpecification65">
            <covered>
              <lifelineMoniker
                Id="905e99ed-5eb8-4314-a476-77ed1248e72e"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="983c03a2-9c9f-4df8-87af-de2a45464e0c"
            name="BehaviorExecutionSpecification20">
            <coveredLifelines>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="c6bfcdf9-eb31-4cac-8f66-d6d7b2d38e4a"
                LastKnownName="ExecutionOccurrenceSpecification40"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="09cf42a4-ebf1-475b-901e-745f0ae65526"
                LastKnownName="ExecutionOccurrenceSpecification39"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="f6d9c455-1900-4ee9-92cd-777a6f623162"
                LastKnownName="MessageOccurrenceSpecification70"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="5c95ab07-d24a-481b-ba8b-624274bfec8d"
                LastKnownName="MessageOccurrenceSpecification73"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="73356055-4e61-40e8-bda2-567ca9ad0e13"
                LastKnownName="MessageOccurrenceSpecification76"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="4e0a190f-f5c6-4cf4-a398-71b58641bdf9"
                LastKnownName="MessageOccurrenceSpecification71"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="09cf42a4-ebf1-475b-901e-745f0ae65526"
            name="ExecutionOccurrenceSpecification39">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="7189d28b-1b0a-4b6d-9d03-c745f594a5a8"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="cc6bbac6-6233-486f-89da-4c92f33d5088"
            name="MessageOccurrenceSpecification69">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="f6d9c455-1900-4ee9-92cd-777a6f623162"
            name="MessageOccurrenceSpecification70">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="75ab144a-e677-4376-a9e4-190876f8bd91"
            name="BehaviorExecutionSpecification21">
            <coveredLifelines>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="8ccfde60-9a1a-471e-8f28-8de369f9d0ac"
                LastKnownName="ExecutionOccurrenceSpecification42"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="77cf0ae9-65b8-49a2-a7e2-a4c9ce33b7fd"
                LastKnownName="ExecutionOccurrenceSpecification41"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="e6742f9f-c6c6-410c-94d7-c099b61cdd28"
                LastKnownName="MessageOccurrenceSpecification74"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="3315b728-5636-4909-9a6a-af1a8ea63f81"
                LastKnownName="MessageOccurrenceSpecification75"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="77cf0ae9-65b8-49a2-a7e2-a4c9ce33b7fd"
            name="ExecutionOccurrenceSpecification41">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="3a960bab-7aba-414e-836c-71d0d14680aa"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="e6742f9f-c6c6-410c-94d7-c099b61cdd28"
            name="MessageOccurrenceSpecification74">
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="5c95ab07-d24a-481b-ba8b-624274bfec8d"
            name="MessageOccurrenceSpecification73">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="3315b728-5636-4909-9a6a-af1a8ea63f81"
            name="MessageOccurrenceSpecification75">
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="73356055-4e61-40e8-bda2-567ca9ad0e13"
            name="MessageOccurrenceSpecification76">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="8ccfde60-9a1a-471e-8f28-8de369f9d0ac"
            name="ExecutionOccurrenceSpecification42">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="7e1b35fc-8010-4990-b560-029761c6e42c"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="433ac68a-1a54-404b-a20d-130d5030078c"
            name="MessageOccurrenceSpecification72">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="4e0a190f-f5c6-4cf4-a398-71b58641bdf9"
            name="MessageOccurrenceSpecification71">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="c6bfcdf9-eb31-4cac-8f66-d6d7b2d38e4a"
            name="ExecutionOccurrenceSpecification40">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="f4f64d07-d2dd-4cf5-af43-025d32bb9f40"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="6347478b-0ac7-41e5-a657-ca5d7e6f51bb"
            name="MessageOccurrenceSpecification68">
            <covered>
              <lifelineMoniker
                Id="905e99ed-5eb8-4314-a476-77ed1248e72e"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="f20b11d0-b7ae-4e94-8ce8-37eb729221f9"
            name="MessageOccurrenceSpecification67">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="b23c66fc-afab-493c-a071-65ec61b47428"
            name="ExecutionOccurrenceSpecification38">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="18f02bf0-2ae3-4504-810b-e61c782e06eb"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="7af295d0-f2cd-44de-8b29-19496afa490c"
            name="OperandOccurrenceSpecification20">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="889e6dff-6ccb-4431-b980-6ced82cbf39f"
            name="OperandOccurrenceSpecification22">
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="4b253f1a-238f-4b7b-943d-488e7498a032"
            name="OperandOccurrenceSpecification24">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="58df0eda-8d12-4051-a6e5-57a0179a98b8"
            name="OperandOccurrenceSpecification28">
            <covered>
              <lifelineMoniker
                Id="905e99ed-5eb8-4314-a476-77ed1248e72e"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="8de9d2a2-90b2-4055-9ffb-fe5638c0c7c1"
            name="OperandOccurrenceSpecification8">
            <covered>
              <lifelineMoniker
                Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="41fbe7f7-693a-41b5-8544-f90f316c5350"
            name="OperandOccurrenceSpecification12">
            <covered>
              <lifelineMoniker
                Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="644bcea7-14a1-4ab4-ab6c-6955410491e1"
            name="OperandOccurrenceSpecification10">
            <covered>
              <lifelineMoniker
                Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
                LastKnownName="DeviceAPI"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="34111cd1-5050-4218-8b5f-347db698ec61"
            name="OperandOccurrenceSpecification30">
            <covered>
              <lifelineMoniker
                Id="905e99ed-5eb8-4314-a476-77ed1248e72e"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
        </fragments>
        <lifelines>
          <lifeline
            Id="905e99ed-5eb8-4314-a476-77ed1248e72e"
            name="Controller"
            isActor="false"
            lifelineDisplayName="Controller">
            <topLevelOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="b5ebaa76-deac-4615-b79a-aad2a9d7b0b7"
                LastKnownName="MessageOccurrenceSpecification21"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="9d2d450b-3376-490f-adf5-59e9351e58da"
                LastKnownName="OperandOccurrenceSpecification29"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="8d57b6f0-a031-4fa1-8aa0-63b0362d9a71"
                LastKnownName="OperandOccurrenceSpecification25"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="c1b7ed32-0463-4be3-91a5-31ddf53fbba0"
                LastKnownName="OperandOccurrenceSpecification26"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="e9c9bb27-eae6-4068-abba-da364a18e519"
                LastKnownName="OperandOccurrenceSpecification27"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="1eecbfc6-2d95-4f2a-865e-a2d790ac765b"
                LastKnownName="MessageOccurrenceSpecification65"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="6347478b-0ac7-41e5-a657-ca5d7e6f51bb"
                LastKnownName="MessageOccurrenceSpecification68"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="58df0eda-8d12-4051-a6e5-57a0179a98b8"
                LastKnownName="OperandOccurrenceSpecification28"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="34111cd1-5050-4218-8b5f-347db698ec61"
                LastKnownName="OperandOccurrenceSpecification30"
                LastKnownLocation="models-camp-us2.uml" />
            </topLevelOccurrences>
          </lifeline>
          <lifeline
            Id="bc5fa0a9-e0e1-4442-9a42-4cfaed80be6a"
            name=": AbstractInput"
            isActor="false"
            lifelineDisplayName=": AbstractInput">
            <topLevelOccurrences>
              <executionOccurrenceSpecificationMoniker
                Id="4a7ed2ef-3770-4e51-a646-0dbc6e55beec"
                LastKnownName="ExecutionOccurrenceSpecification11"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="6988a07e-e1b8-4b22-86f2-670da9bacb68"
                LastKnownName="ExecutionOccurrenceSpecification12"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="1b5bee7f-7e00-495b-bf58-f4f92b281b2b"
                LastKnownName="OperandOccurrenceSpecification11"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="8e03ef36-2643-4c01-9c96-64a5aa71cd53"
                LastKnownName="OperandOccurrenceSpecification17"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="bb0d67c9-19b1-4ecd-9529-07613c31f234"
                LastKnownName="ExecutionOccurrenceSpecification33"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="3c844b2c-4806-4591-bd84-72ecb3f7990c"
                LastKnownName="ExecutionOccurrenceSpecification34"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="b3958325-d109-4c48-a906-bdf8d0a04f97"
                LastKnownName="OperandOccurrenceSpecification18"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="0681c4e1-c0c3-4724-b53c-60fbec526451"
                LastKnownName="OperandOccurrenceSpecification23"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="d6a858c2-fa35-42a4-a292-cf6f48f5f000"
                LastKnownName="ExecutionOccurrenceSpecification37"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="b23c66fc-afab-493c-a071-65ec61b47428"
                LastKnownName="ExecutionOccurrenceSpecification38"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="4b253f1a-238f-4b7b-943d-488e7498a032"
                LastKnownName="OperandOccurrenceSpecification24"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="41fbe7f7-693a-41b5-8544-f90f316c5350"
                LastKnownName="OperandOccurrenceSpecification12"
                LastKnownLocation="models-camp-us2.uml" />
            </topLevelOccurrences>
          </lifeline>
          <lifeline
            Id="c7827f70-c73e-410f-89f2-1eb51ad6aec5"
            name=": InputDevice:AbstractInput"
            isActor="false"
            lifelineDisplayName=": InputDevice:AbstractInput">
            <topLevelOccurrences>
              <executionOccurrenceSpecificationMoniker
                Id="33d937ef-93d0-49b9-a899-481b388c4b0c"
                LastKnownName="ExecutionOccurrenceSpecification15"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="c3252457-a4b8-4f43-9f80-540bf943b3e2"
                LastKnownName="ExecutionOccurrenceSpecification16"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="2e269abd-2208-42ea-b8af-863f02d0704a"
                LastKnownName="OperandOccurrenceSpecification7"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="b8b0f3ce-c493-4525-94d6-0967582f8cfb"
                LastKnownName="OperandOccurrenceSpecification13"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="85da0ffe-6d83-4611-b8a5-8a6da9490d37"
                LastKnownName="ExecutionOccurrenceSpecification29"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="67d0b923-e3bc-41cd-b147-dd2b0c79c3b7"
                LastKnownName="ExecutionOccurrenceSpecification30"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="35bab701-25b1-4974-a059-41344f2a778c"
                LastKnownName="OperandOccurrenceSpecification14"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="addbf177-74e8-491d-bb7d-6a5b056808a3"
                LastKnownName="OperandOccurrenceSpecification19"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="09cf42a4-ebf1-475b-901e-745f0ae65526"
                LastKnownName="ExecutionOccurrenceSpecification39"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="c6bfcdf9-eb31-4cac-8f66-d6d7b2d38e4a"
                LastKnownName="ExecutionOccurrenceSpecification40"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="7af295d0-f2cd-44de-8b29-19496afa490c"
                LastKnownName="OperandOccurrenceSpecification20"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="8de9d2a2-90b2-4055-9ffb-fe5638c0c7c1"
                LastKnownName="OperandOccurrenceSpecification8"
                LastKnownLocation="models-camp-us2.uml" />
            </topLevelOccurrences>
          </lifeline>
          <lifeline
            Id="f2d0493c-723d-450c-9622-adb8d5a0ea62"
            name="DeviceAPI"
            isActor="false"
            lifelineDisplayName="DeviceAPI">
            <topLevelOccurrences>
              <executionOccurrenceSpecificationMoniker
                Id="0f2327fc-143f-4684-9ae2-840f9ae600a2"
                LastKnownName="ExecutionOccurrenceSpecification25"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="b6988e68-980c-40c2-8546-44c15fa6b512"
                LastKnownName="ExecutionOccurrenceSpecification26"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="a9d248fd-8bfa-47d8-9901-7c83f1fe7312"
                LastKnownName="OperandOccurrenceSpecification9"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="c430ed2a-f8db-46ab-a7b5-e8df0ff39b6d"
                LastKnownName="OperandOccurrenceSpecification15"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="2078f5ff-bbc9-464b-b5d3-c7e2d0393e86"
                LastKnownName="MessageOccurrenceSpecification51"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="c7cd8097-ce2d-4c96-89c6-ceee6bdd8db7"
                LastKnownName="MessageOccurrenceSpecification54"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="2234f129-ef43-458d-8815-102d0408170c"
                LastKnownName="OperandOccurrenceSpecification16"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="249ff49f-3682-4572-9d00-6f987d744724"
                LastKnownName="OperandOccurrenceSpecification21"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="77cf0ae9-65b8-49a2-a7e2-a4c9ce33b7fd"
                LastKnownName="ExecutionOccurrenceSpecification41"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="8ccfde60-9a1a-471e-8f28-8de369f9d0ac"
                LastKnownName="ExecutionOccurrenceSpecification42"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="889e6dff-6ccb-4431-b980-6ced82cbf39f"
                LastKnownName="OperandOccurrenceSpecification22"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="644bcea7-14a1-4ab4-ab6c-6955410491e1"
                LastKnownName="OperandOccurrenceSpecification10"
                LastKnownLocation="models-camp-us2.uml" />
            </topLevelOccurrences>
          </lifeline>
        </lifelines>
        <messages>
          <message
            Id="dea66465-4c33-49d1-821b-433cd931814d"
            name="start (in new thread)"
            messageKind="Complete"
            messageSort="AsynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="b5ebaa76-deac-4615-b79a-aad2a9d7b0b7"
                LastKnownName="MessageOccurrenceSpecification21"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="066eb57b-220f-42c4-87ce-bd8f33174a08"
                LastKnownName="MessageOccurrenceSpecification22"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="383c9309-132b-4329-b9fb-8300025d0303"
            name="setRunning(true)"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="true">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="d83f43ff-ee0e-42eb-b0e4-20e31999df91"
                LastKnownName="MessageOccurrenceSpecification23"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="6cac31e5-98f2-4fa2-babd-275591b38612"
                LastKnownName="MessageOccurrenceSpecification24"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="ebf2e1a8-4b04-4ca3-902f-5d69809a7862"
            name="startAcquisition()"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="c04c08d3-4cc7-4efe-8f99-df722d89b54e"
                LastKnownName="MessageOccurrenceSpecification25"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="0fcc468b-dbb0-475c-8be0-a08f05420bf2"
                LastKnownName="MessageOccurrenceSpecification26"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="42e43066-8906-460f-b3fb-893471bd75ce"
            name="setup callback"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="438a5457-0475-41c8-8d97-ad5b7c91e9aa"
                LastKnownName="MessageOccurrenceSpecification45"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="302cfccd-3ef4-48a1-bddc-d9b96a85940b"
                LastKnownName="MessageOccurrenceSpecification46"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="31f5018f-83c3-4d50-a532-4c396bf13fb4"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="5070cb35-cb95-41a1-a987-3aa7aa46e593"
                LastKnownName="MessageOccurrenceSpecification47"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="d38cf595-7357-4928-b201-c73db168d72c"
                LastKnownName="MessageOccurrenceSpecification48"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="88659a1d-7a15-4161-81db-7af09ca9b77c"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="accd6930-ac21-4376-8fb6-30dd6d3c1c0f"
                LastKnownName="MessageOccurrenceSpecification27"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="b159f566-abf7-4080-8453-0079508fa8c1"
                LastKnownName="MessageOccurrenceSpecification28"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="d26cf57f-dd30-4695-9095-74595f8c5325"
            name="callback"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="2078f5ff-bbc9-464b-b5d3-c7e2d0393e86"
                LastKnownName="MessageOccurrenceSpecification51"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="486410f0-7ee8-45dc-acb0-ba209de8027a"
                LastKnownName="MessageOccurrenceSpecification52"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="09cd8164-b5f5-4c22-b7fe-4d314b484b61"
            name="generate data object"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="true">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="6a1ddf6e-9df1-4b79-859a-fefb852632e1"
                LastKnownName="MessageOccurrenceSpecification55"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="7199d3f9-1818-45f3-9390-c8f956fb5ea7"
                LastKnownName="MessageOccurrenceSpecification56"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="54a21d77-338a-4a63-88d6-8518819fb3a1"
            name="addData()"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="3e2a6e09-4241-4a12-8b10-a6c83ee3895c"
                LastKnownName="MessageOccurrenceSpecification57"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="99bcdd99-9acf-4022-8bfb-b0e113882cde"
                LastKnownName="MessageOccurrenceSpecification58"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="cc43b505-c26d-45f2-99f5-b34e4c336eee"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="c6250cc4-63d6-4548-8d21-afcce334d9a9"
                LastKnownName="MessageOccurrenceSpecification59"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="ce4720f3-4bcd-470d-a2e3-559d967e0ec6"
                LastKnownName="MessageOccurrenceSpecification60"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="b4241b40-2527-44e3-bb3f-2288c84bf902"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="8b339fdc-34a8-47e2-903a-14cc4923073e"
                LastKnownName="MessageOccurrenceSpecification53"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="c7cd8097-ce2d-4c96-89c6-ceee6bdd8db7"
                LastKnownName="MessageOccurrenceSpecification54"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="021cdacb-8218-4b01-83eb-de4f51d8977c"
            name="setRunning(false)"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="1eecbfc6-2d95-4f2a-865e-a2d790ac765b"
                LastKnownName="MessageOccurrenceSpecification65"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="596e8bdd-8e43-4976-9d8b-479d43582cd3"
                LastKnownName="MessageOccurrenceSpecification66"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="19f149b6-5194-4fbf-95c5-d98cd2283a2e"
            name="stopAcquisition()"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="cc6bbac6-6233-486f-89da-4c92f33d5088"
                LastKnownName="MessageOccurrenceSpecification69"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="f6d9c455-1900-4ee9-92cd-777a6f623162"
                LastKnownName="MessageOccurrenceSpecification70"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="89525912-7c98-4795-af52-3d66454bddaf"
            name="unregister callback"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="5c95ab07-d24a-481b-ba8b-624274bfec8d"
                LastKnownName="MessageOccurrenceSpecification73"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="e6742f9f-c6c6-410c-94d7-c099b61cdd28"
                LastKnownName="MessageOccurrenceSpecification74"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="cd6f98bb-d931-4b52-8780-b302ec88aa7e"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="3315b728-5636-4909-9a6a-af1a8ea63f81"
                LastKnownName="MessageOccurrenceSpecification75"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="73356055-4e61-40e8-bda2-567ca9ad0e13"
                LastKnownName="MessageOccurrenceSpecification76"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="0dde27eb-07cd-4067-b76b-ff70fb140b0a"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="4e0a190f-f5c6-4cf4-a398-71b58641bdf9"
                LastKnownName="MessageOccurrenceSpecification71"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="433ac68a-1a54-404b-a20d-130d5030078c"
                LastKnownName="MessageOccurrenceSpecification72"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="5829e911-6ba2-47ef-8e7a-1cb9e6ba4549"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="f20b11d0-b7ae-4e94-8ce8-37eb729221f9"
                LastKnownName="MessageOccurrenceSpecification67"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="6347478b-0ac7-41e5-a657-ca5d7e6f51bb"
                LastKnownName="MessageOccurrenceSpecification68"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
        </messages>
      </interaction>
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="adfb1da3-e9a9-4a9b-aa21-952914013e3c"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="940420c5-df65-4ae8-8d94-af62cbecfa5b"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="59a61876-1bd5-4182-8858-f7bc2caef407"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="21560ab3-7788-4ec7-b382-4b351cad092e"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="c1433b9f-47e8-4e68-a484-8ab2df4b0b66"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="d649eac8-475e-4194-9c97-3ec6308b955f"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="23ede670-8fd4-44f7-abcd-4ddba139a261"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="d7edc92d-c5c0-4c13-92a4-a58ea9720bd4"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="ac558aa7-059d-42c0-9eb2-40be67b78f44"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="d993c360-abe9-45d6-9818-e8f27d93a347"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="1dc5ef5a-c372-4aaa-b6ca-fb770e5fa1c0"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="1fb374a9-4f5c-4520-9183-11ae5e65a12c"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="5b472ce4-8030-48fc-9376-daab67543e4b"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="e7a9b9cf-5803-450f-a887-90b566424584"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="54c42578-7f87-4d6d-afea-4b6fe020d6d5"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="3bfd9d8b-e24d-4c1b-b1d9-952498dd177d"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="7b9dc3bc-6bdd-4f95-96d1-281f9d2e9768"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="af8bf7ea-37b5-4cd5-9295-adf60023495a"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <interaction
        Id="f87ee90f-9534-4625-97ae-34cfbc8ec286"
        name="SequenceInputStreamPolling"
        collapseFragmentsFlag="false"
        isActiveClass="false"
        isAbstract="false"
        isLeaf="false"
        isReentrant="false">
        <fragments>
          <behaviorExecutionSpecification
            Id="9ce45e45-c345-4017-96be-46cc6df0bf22"
            name="BehaviorExecutionSpecification6">
            <coveredLifelines>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="f73777ed-378f-4954-a2c1-51c4b980d6d2"
                LastKnownName="ExecutionOccurrenceSpecification12"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="2531dd2c-ece4-41f2-a174-e4a28b90ebe2"
                LastKnownName="ExecutionOccurrenceSpecification11"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="be0557f2-62d0-44f6-a0eb-eca82161df6f"
                LastKnownName="MessageOccurrenceSpecification22"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="7e5ba3c6-a59d-4b45-846b-41a65336f6c9"
                LastKnownName="MessageOccurrenceSpecification23"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="1ea6c33e-f24e-4922-a0d6-35370af9419e"
                LastKnownName="ExecutionOccurrenceSpecification13"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="388c7f7f-9cb3-443f-8333-c4f93220c429"
                LastKnownName="ExecutionOccurrenceSpecification14"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="5a8e0d96-fa90-4866-9762-0143356762d0"
                LastKnownName="MessageOccurrenceSpecification25"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="2f6e7087-cefe-4022-a4d9-d284fdc9d4b7"
                LastKnownName="OperandOccurrenceSpecification9"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="50cee015-5c6a-4e3d-9158-f60d0c7a921e"
                LastKnownName="ExecutionOccurrenceSpecification33"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="0f09380d-b6e2-475f-a3b8-0a51c7647e6a"
                LastKnownName="ExecutionOccurrenceSpecification34"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="10e4750d-f09f-4940-a3f1-6a9b8d1b7ba2"
                LastKnownName="OperandOccurrenceSpecification10"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="b70718b7-63e1-42ed-8ce1-a875e94c9d6d"
                LastKnownName="MessageOccurrenceSpecification28"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="2531dd2c-ece4-41f2-a174-e4a28b90ebe2"
            name="ExecutionOccurrenceSpecification11">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="03399260-c828-45e3-8875-321b28b57e5d"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="66b80e67-0220-48af-ab3a-aee279ce95e5"
            name="MessageOccurrenceSpecification21">
            <covered>
              <lifelineMoniker
                Id="ca955178-9b53-42dc-b263-f6b924be05bc"
                LastKnownName="Controller"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="be0557f2-62d0-44f6-a0eb-eca82161df6f"
            name="MessageOccurrenceSpecification22">
            <covered>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="7e5ba3c6-a59d-4b45-846b-41a65336f6c9"
            name="MessageOccurrenceSpecification23">
            <covered>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="9ab42383-9f6f-448b-8f61-fabab30cb84a"
            name="BehaviorExecutionSpecification7">
            <coveredLifelines>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="388c7f7f-9cb3-443f-8333-c4f93220c429"
                LastKnownName="ExecutionOccurrenceSpecification14"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="1ea6c33e-f24e-4922-a0d6-35370af9419e"
                LastKnownName="ExecutionOccurrenceSpecification13"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="a6a814ba-6871-401f-8335-b9f6a6b78528"
                LastKnownName="MessageOccurrenceSpecification24"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="1ea6c33e-f24e-4922-a0d6-35370af9419e"
            name="ExecutionOccurrenceSpecification13">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="06e322ed-8129-458c-9d3d-4b6649e8cbb3"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="a6a814ba-6871-401f-8335-b9f6a6b78528"
            name="MessageOccurrenceSpecification24">
            <covered>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="388c7f7f-9cb3-443f-8333-c4f93220c429"
            name="ExecutionOccurrenceSpecification14">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="16cee806-ba4e-4737-8c8c-e7e90980ce9c"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="dfe5d8b5-3393-410b-b90e-f2592ee3fcd6"
            name="BehaviorExecutionSpecification8">
            <coveredLifelines>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="073ba42b-703f-4d28-8965-383509e2db09"
                LastKnownName="ExecutionOccurrenceSpecification16"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="798bb54d-5eb0-4356-a71b-bb9038de9473"
                LastKnownName="ExecutionOccurrenceSpecification15"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="41910fcc-78d9-48b7-815a-a6feede14763"
                LastKnownName="MessageOccurrenceSpecification26"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="33d4df77-e3e7-45e2-810a-47cee05e4222"
                LastKnownName="OperandOccurrenceSpecification7"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="da0b8ace-b8e3-4c47-87b8-8fcd7d53de5d"
                LastKnownName="MessageOccurrenceSpecification61"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="827c871b-4cc3-4d12-aea0-fa8473106061"
                LastKnownName="ExecutionOccurrenceSpecification35"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="4f70a18d-4090-464c-b518-d9d2c8f327fb"
                LastKnownName="ExecutionOccurrenceSpecification36"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="020ae99d-5a2d-40cc-8c45-47388bca3f1f"
                LastKnownName="MessageOccurrenceSpecification55"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="256ab18e-ed5a-4ded-9e37-18e5d8c36a6a"
                LastKnownName="ExecutionOccurrenceSpecification31"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="f55ef419-b47b-45b1-a1d6-5e0a73e98a44"
                LastKnownName="ExecutionOccurrenceSpecification32"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="d287f301-e5bc-4291-a057-1495ddd2cc2d"
                LastKnownName="MessageOccurrenceSpecification57"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="32030cdf-a130-4224-9a88-23af73afdf0f"
                LastKnownName="MessageOccurrenceSpecification60"
                LastKnownLocation="models-camp-us2.uml" />
              <operandOccurrenceSpecificationMoniker
                Id="408093c1-659d-4064-b183-ea44b465935b"
                LastKnownName="OperandOccurrenceSpecification8"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="e3628edd-1e9c-4947-934f-52c1ff8aa074"
                LastKnownName="MessageOccurrenceSpecification27"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="798bb54d-5eb0-4356-a71b-bb9038de9473"
            name="ExecutionOccurrenceSpecification15">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="cfde2823-f260-427f-8a4f-56d68199570c"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="41910fcc-78d9-48b7-815a-a6feede14763"
            name="MessageOccurrenceSpecification26">
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="5a8e0d96-fa90-4866-9762-0143356762d0"
            name="MessageOccurrenceSpecification25">
            <covered>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <combinedFragment
            Id="7beac85e-8797-4dc9-8753-bc1f8c5dbf50"
            name="CombinedFragment1"
            interactionOperator="Loop">
            <coveredLifelines>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <operands>
              <interactionOperand
                Id="c096f3b0-f52f-4dd1-a023-abfedacfb0f1"
                name="InteractionOperand1">
                <coveredLifelines>
                  <lifelineMoniker
                    Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                    LastKnownName=": AbstractInput"
                    LastKnownLocation="models-camp-us2.uml" />
                  <lifelineMoniker
                    Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                    LastKnownName=": InputDevice:AbstractInput"
                    LastKnownLocation="models-camp-us2.uml" />
                </coveredLifelines>
                <guard>
                  <interactionConstraint
                    Id="5fee7e46-9f11-4dae-ac0d-b0f449737680"
                    guardText="while(running)">
                    <maxInt>
                      <literalString
                        Id="b1f24e7e-98aa-4e54-be43-6f23e3e2d463"
                        name="LiteralString1" />
                    </maxInt>
                    <minInt>
                      <literalString
                        Id="7f1b2ac6-f9f6-4aea-885d-12a7c25c2f53"
                        name="LiteralString2" />
                    </minInt>
                  </interactionConstraint>
                </guard>
                <operandOccurrenceSpecifications>
                  <operandOccurrenceSpecificationMoniker
                    Id="33d4df77-e3e7-45e2-810a-47cee05e4222"
                    LastKnownName="OperandOccurrenceSpecification7"
                    LastKnownLocation="models-camp-us2.uml" />
                  <operandOccurrenceSpecificationMoniker
                    Id="408093c1-659d-4064-b183-ea44b465935b"
                    LastKnownName="OperandOccurrenceSpecification8"
                    LastKnownLocation="models-camp-us2.uml" />
                  <operandOccurrenceSpecificationMoniker
                    Id="2f6e7087-cefe-4022-a4d9-d284fdc9d4b7"
                    LastKnownName="OperandOccurrenceSpecification9"
                    LastKnownLocation="models-camp-us2.uml" />
                  <operandOccurrenceSpecificationMoniker
                    Id="10e4750d-f09f-4940-a3f1-6a9b8d1b7ba2"
                    LastKnownName="OperandOccurrenceSpecification10"
                    LastKnownLocation="models-camp-us2.uml" />
                </operandOccurrenceSpecifications>
              </interactionOperand>
            </operands>
          </combinedFragment>
          <operandOccurrenceSpecification
            Id="2f6e7087-cefe-4022-a4d9-d284fdc9d4b7"
            name="OperandOccurrenceSpecification9">
            <covered>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="33d4df77-e3e7-45e2-810a-47cee05e4222"
            name="OperandOccurrenceSpecification7">
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="da0b8ace-b8e3-4c47-87b8-8fcd7d53de5d"
            name="MessageOccurrenceSpecification61">
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="5403728e-c128-4a8a-9499-5eec010861fe"
            name="BehaviorExecutionSpecification18">
            <coveredLifelines>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="4f70a18d-4090-464c-b518-d9d2c8f327fb"
                LastKnownName="ExecutionOccurrenceSpecification36"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="827c871b-4cc3-4d12-aea0-fa8473106061"
                LastKnownName="ExecutionOccurrenceSpecification35"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="c9a37e64-5dd5-4cd0-9c10-42c9fc5f53ec"
                LastKnownName="MessageOccurrenceSpecification62"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="827c871b-4cc3-4d12-aea0-fa8473106061"
            name="ExecutionOccurrenceSpecification35">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="9320d74b-a681-49ca-bdd1-3695162a83a5"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="c9a37e64-5dd5-4cd0-9c10-42c9fc5f53ec"
            name="MessageOccurrenceSpecification62">
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="4f70a18d-4090-464c-b518-d9d2c8f327fb"
            name="ExecutionOccurrenceSpecification36">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="bf8601e5-f1ab-472d-a211-e98f1b8e5b31"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="020ae99d-5a2d-40cc-8c45-47388bca3f1f"
            name="MessageOccurrenceSpecification55">
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="c800e770-2fa5-475b-8f35-1f6966a8e39b"
            name="BehaviorExecutionSpecification16">
            <coveredLifelines>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="f55ef419-b47b-45b1-a1d6-5e0a73e98a44"
                LastKnownName="ExecutionOccurrenceSpecification32"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="256ab18e-ed5a-4ded-9e37-18e5d8c36a6a"
                LastKnownName="ExecutionOccurrenceSpecification31"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="cd25957e-2e21-43c9-bb7f-fe9ebf197253"
                LastKnownName="MessageOccurrenceSpecification56"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="256ab18e-ed5a-4ded-9e37-18e5d8c36a6a"
            name="ExecutionOccurrenceSpecification31">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="37a1a40c-c9eb-4e83-8d9c-18ff0441c941"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="cd25957e-2e21-43c9-bb7f-fe9ebf197253"
            name="MessageOccurrenceSpecification56">
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="f55ef419-b47b-45b1-a1d6-5e0a73e98a44"
            name="ExecutionOccurrenceSpecification32">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="639f77b8-253b-4761-82f4-4c83703142a8"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <behaviorExecutionSpecification
            Id="d8b81ec8-b2ac-4c6e-85d8-4604b08d6279"
            name="BehaviorExecutionSpecification17">
            <coveredLifelines>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </coveredLifelines>
            <finish>
              <executionOccurrenceSpecificationMoniker
                Id="0f09380d-b6e2-475f-a3b8-0a51c7647e6a"
                LastKnownName="ExecutionOccurrenceSpecification34"
                LastKnownLocation="models-camp-us2.uml" />
            </finish>
            <start>
              <executionOccurrenceSpecificationMoniker
                Id="50cee015-5c6a-4e3d-9158-f60d0c7a921e"
                LastKnownName="ExecutionOccurrenceSpecification33"
                LastKnownLocation="models-camp-us2.uml" />
            </start>
            <nestedOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="886250b9-a98c-4ecd-a930-c238bd97fe6c"
                LastKnownName="MessageOccurrenceSpecification58"
                LastKnownLocation="models-camp-us2.uml" />
              <messageOccurrenceSpecificationMoniker
                Id="efc8f427-fde9-4d3c-954d-ee80a8b98810"
                LastKnownName="MessageOccurrenceSpecification59"
                LastKnownLocation="models-camp-us2.uml" />
            </nestedOccurrences>
          </behaviorExecutionSpecification>
          <executionOccurrenceSpecification
            Id="50cee015-5c6a-4e3d-9158-f60d0c7a921e"
            name="ExecutionOccurrenceSpecification33">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="6e2c29f6-fc01-4942-91ee-b4fb48318d96"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="886250b9-a98c-4ecd-a930-c238bd97fe6c"
            name="MessageOccurrenceSpecification58">
            <covered>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="d287f301-e5bc-4291-a057-1495ddd2cc2d"
            name="MessageOccurrenceSpecification57">
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="efc8f427-fde9-4d3c-954d-ee80a8b98810"
            name="MessageOccurrenceSpecification59">
            <covered>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="32030cdf-a130-4224-9a88-23af73afdf0f"
            name="MessageOccurrenceSpecification60">
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="0f09380d-b6e2-475f-a3b8-0a51c7647e6a"
            name="ExecutionOccurrenceSpecification34">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="4a0421b2-979c-48c8-9d87-81aa60a6a60c"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="408093c1-659d-4064-b183-ea44b465935b"
            name="OperandOccurrenceSpecification8">
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <operandOccurrenceSpecification
            Id="10e4750d-f09f-4940-a3f1-6a9b8d1b7ba2"
            name="OperandOccurrenceSpecification10">
            <covered>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </operandOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="b70718b7-63e1-42ed-8ce1-a875e94c9d6d"
            name="MessageOccurrenceSpecification28">
            <covered>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <messageOccurrenceSpecification
            Id="e3628edd-1e9c-4947-934f-52c1ff8aa074"
            name="MessageOccurrenceSpecification27">
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </messageOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="073ba42b-703f-4d28-8965-383509e2db09"
            name="ExecutionOccurrenceSpecification16">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="55cd3779-be93-44be-b535-7b6b164bd0f0"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
                LastKnownName=": InputDevice:AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
          <executionOccurrenceSpecification
            Id="f73777ed-378f-4954-a2c1-51c4b980d6d2"
            name="ExecutionOccurrenceSpecification12">
            <event>
              <executionOccurrenceSpecificationReferencesEvent>
                <executionEventMoniker
                  Id="b9b056b5-cf0f-4c08-9df6-6540eb495b27"
                  LastKnownName="ExecutionEvent"
                  LastKnownLocation="models-camp-us2.uml" />
              </executionOccurrenceSpecificationReferencesEvent>
            </event>
            <covered>
              <lifelineMoniker
                Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
                LastKnownName=": AbstractInput"
                LastKnownLocation="models-camp-us2.uml" />
            </covered>
          </executionOccurrenceSpecification>
        </fragments>
        <lifelines>
          <lifeline
            Id="ca955178-9b53-42dc-b263-f6b924be05bc"
            name="Controller"
            isActor="false"
            lifelineDisplayName="Controller">
            <topLevelOccurrences>
              <messageOccurrenceSpecificationMoniker
                Id="66b80e67-0220-48af-ab3a-aee279ce95e5"
                LastKnownName="MessageOccurrenceSpecification21"
                LastKnownLocation="models-camp-us2.uml" />
            </topLevelOccurrences>
          </lifeline>
          <lifeline
            Id="d4190fe0-ce2c-436f-8ea3-476396a86223"
            name=": AbstractInput"
            isActor="false"
            lifelineDisplayName=": AbstractInput">
            <topLevelOccurrences>
              <executionOccurrenceSpecificationMoniker
                Id="2531dd2c-ece4-41f2-a174-e4a28b90ebe2"
                LastKnownName="ExecutionOccurrenceSpecification11"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="f73777ed-378f-4954-a2c1-51c4b980d6d2"
                LastKnownName="ExecutionOccurrenceSpecification12"
                LastKnownLocation="models-camp-us2.uml" />
            </topLevelOccurrences>
          </lifeline>
          <lifeline
            Id="e05ee884-4b2e-4a48-b267-964ea7027c38"
            name=": InputDevice:AbstractInput"
            isActor="false"
            lifelineDisplayName=": InputDevice:AbstractInput">
            <topLevelOccurrences>
              <executionOccurrenceSpecificationMoniker
                Id="798bb54d-5eb0-4356-a71b-bb9038de9473"
                LastKnownName="ExecutionOccurrenceSpecification15"
                LastKnownLocation="models-camp-us2.uml" />
              <executionOccurrenceSpecificationMoniker
                Id="073ba42b-703f-4d28-8965-383509e2db09"
                LastKnownName="ExecutionOccurrenceSpecification16"
                LastKnownLocation="models-camp-us2.uml" />
            </topLevelOccurrences>
          </lifeline>
        </lifelines>
        <messages>
          <message
            Id="ba4a39a3-f476-4808-9549-0ed38866b250"
            name="start (in new thread)"
            messageKind="Complete"
            messageSort="AsynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="66b80e67-0220-48af-ab3a-aee279ce95e5"
                LastKnownName="MessageOccurrenceSpecification21"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="be0557f2-62d0-44f6-a0eb-eca82161df6f"
                LastKnownName="MessageOccurrenceSpecification22"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="26223cee-16e9-487b-a7e9-f29255b4bc32"
            name="setRunning(true)"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="true">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="7e5ba3c6-a59d-4b45-846b-41a65336f6c9"
                LastKnownName="MessageOccurrenceSpecification23"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="a6a814ba-6871-401f-8335-b9f6a6b78528"
                LastKnownName="MessageOccurrenceSpecification24"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="7bd1e866-7055-4535-a37b-00d289f38154"
            name="startAcquisition()"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="5a8e0d96-fa90-4866-9762-0143356762d0"
                LastKnownName="MessageOccurrenceSpecification25"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="41910fcc-78d9-48b7-815a-a6feede14763"
                LastKnownName="MessageOccurrenceSpecification26"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="c91f6e72-0032-4ac1-b617-f61c9917728a"
            name="poll until data available"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="true">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="da0b8ace-b8e3-4c47-87b8-8fcd7d53de5d"
                LastKnownName="MessageOccurrenceSpecification61"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="c9a37e64-5dd5-4cd0-9c10-42c9fc5f53ec"
                LastKnownName="MessageOccurrenceSpecification62"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="4268e84c-6a3c-41a8-8d43-7fe7c0883dad"
            name="generate data object"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="true">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="020ae99d-5a2d-40cc-8c45-47388bca3f1f"
                LastKnownName="MessageOccurrenceSpecification55"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="cd25957e-2e21-43c9-bb7f-fe9ebf197253"
                LastKnownName="MessageOccurrenceSpecification56"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="ac919929-3e09-427d-bcc2-27191212f3a9"
            name="addData()"
            messageKind="Complete"
            messageSort="SynchCall"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="d287f301-e5bc-4291-a057-1495ddd2cc2d"
                LastKnownName="MessageOccurrenceSpecification57"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="886250b9-a98c-4ecd-a930-c238bd97fe6c"
                LastKnownName="MessageOccurrenceSpecification58"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="56f7fe2f-4020-4515-bfe7-8feedea6a594"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="efc8f427-fde9-4d3c-954d-ee80a8b98810"
                LastKnownName="MessageOccurrenceSpecification59"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="32030cdf-a130-4224-9a88-23af73afdf0f"
                LastKnownName="MessageOccurrenceSpecification60"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
          <message
            Id="7773796b-80c6-4195-b1be-777c3c335392"
            messageKind="Complete"
            messageSort="Reply"
            createSelfMessage="false">
            <sendEvent>
              <messageOccurrenceSpecificationMoniker
                Id="e3628edd-1e9c-4947-934f-52c1ff8aa074"
                LastKnownName="MessageOccurrenceSpecification27"
                LastKnownLocation="models-camp-us2.uml" />
            </sendEvent>
            <receiveEvent>
              <messageOccurrenceSpecificationMoniker
                Id="b70718b7-63e1-42ed-8ce1-a875e94c9d6d"
                LastKnownName="MessageOccurrenceSpecification28"
                LastKnownLocation="models-camp-us2.uml" />
            </receiveEvent>
          </message>
        </messages>
      </interaction>
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="03399260-c828-45e3-8875-321b28b57e5d"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="b9b056b5-cf0f-4c08-9df6-6540eb495b27"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="cfde2823-f260-427f-8a4f-56d68199570c"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="55cd3779-be93-44be-b535-7b6b164bd0f0"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="06e322ed-8129-458c-9d3d-4b6649e8cbb3"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="16cee806-ba4e-4737-8c8c-e7e90980ce9c"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="37a1a40c-c9eb-4e83-8d9c-18ff0441c941"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="639f77b8-253b-4761-82f4-4c83703142a8"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="6e2c29f6-fc01-4942-91ee-b4fb48318d96"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="4a0421b2-979c-48c8-9d87-81aa60a6a60c"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="9320d74b-a681-49ca-bdd1-3695162a83a5"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="bf8601e5-f1ab-472d-a211-e98f1b8e5b31"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="e4ae2b91-29c9-4003-8501-9a9844d5fe32"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="18f02bf0-2ae3-4504-810b-e61c782e06eb"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="7189d28b-1b0a-4b6d-9d03-c745f594a5a8"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="f4f64d07-d2dd-4cf5-af43-025d32bb9f40"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="3a960bab-7aba-414e-836c-71d0d14680aa"
        name="ExecutionEvent" />
    </packageHasNamedElement>
    <packageHasNamedElement>
      <executionEvent
        Id="7e1b35fc-8010-4990-b560-029761c6e42c"
        name="ExecutionEvent" />
    </packageHasNamedElement>
  </packagedElements>
  <primitiveType
    Id="220a3521-e091-4221-bae9-3ef9018e845c"
    name="Integer"
    isAbstract="false"
    isLeaf="false" />
  <primitiveType
    Id="8943dc84-709e-4f62-b15a-a3273aa6f165"
    name="Boolean"
    isAbstract="false"
    isLeaf="false" />
  <primitiveType
    Id="59259974-6d55-42c6-b7bd-763d77ac8ef9"
    name="String"
    isAbstract="false"
    isLeaf="false" />
  <primitiveType
    Id="3ab42e7d-4969-445a-b209-471f5cb8209c"
    name="UnlimitedNatural"
    isAbstract="false"
    isLeaf="false" />
</modelStoreModel>