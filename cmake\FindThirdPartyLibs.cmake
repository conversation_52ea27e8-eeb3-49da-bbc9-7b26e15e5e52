# FindThirdPartyLibs.cmake
# Unified third-party library finder for SUPRA project
# This module helps find all third-party libraries from the thirdparty/ directory

# Set the base path for third-party libraries
set(THIRDPARTY_BASE_DIR "${CMAKE_SOURCE_DIR}/thirdparty")

# Function to find a library in the thirdparty directory
function(find_thirdparty_library LIB_NAME)
    set(LIB_DIR "${THIRDPARTY_BASE_DIR}/${LIB_NAME}")
    
    if(EXISTS "${LIB_DIR}")
        # Set include directory
        if(EXISTS "${LIB_DIR}/include")
            set(${LIB_NAME}_INCLUDE_DIR "${LIB_DIR}/include" PARENT_SCOPE)
        endif()
        
        # Set library directory
        if(EXISTS "${LIB_DIR}/lib")
            set(${LIB_NAME}_LIBRARY_DIR "${LIB_DIR}/lib" PARENT_SCOPE)
        endif()
        
        # Set binary directory
        if(EXISTS "${LIB_DIR}/bin")
            set(${LIB_NAME}_BINARY_DIR "${LIB_DIR}/bin" PARENT_SCOPE)
        endif()
        
        # Mark as found
        set(${LIB_NAME}_FOUND TRUE PARENT_SCOPE)
        message(STATUS "Found ${LIB_NAME} in ${LIB_DIR}")
    else()
        set(${LIB_NAME}_FOUND FALSE PARENT_SCOPE)
        message(WARNING "${LIB_NAME} not found in ${LIB_DIR}")
    endif()
endfunction()

# Find all required third-party libraries
find_thirdparty_library(oneTBB)
find_thirdparty_library(OpenIGTLink)
find_thirdparty_library(ITK)
find_thirdparty_library(cpprestsdk)
find_thirdparty_library(boost)
find_thirdparty_library(libtorch)

# Set TBB specific variables for compatibility
if(oneTBB_FOUND)
    set(TBB_INCLUDE_DIR ${oneTBB_INCLUDE_DIR})
    set(TBB_LIBRARY_DIR ${oneTBB_LIBRARY_DIR})
    set(TBB_BINARY_DIR ${oneTBB_BINARY_DIR})
    set(TBB_FOUND TRUE)
endif()

# Set OpenIGTLink specific variables
if(OpenIGTLink_FOUND)
    set(OpenIGTLink_INCLUDE_DIRS ${OpenIGTLink_INCLUDE_DIR})
    set(OpenIGTLink_LIBRARY_DIRS ${OpenIGTLink_LIBRARY_DIR})
endif()

# Set ITK specific variables
if(ITK_FOUND)
    set(ITK_INCLUDE_DIRS ${ITK_INCLUDE_DIR})
    set(ITK_LIBRARY_DIRS ${ITK_LIBRARY_DIR})
endif()

# Set Boost specific variables
if(boost_FOUND)
    set(Boost_INCLUDE_DIR ${boost_INCLUDE_DIR})
    set(Boost_LIBRARY_DIR ${boost_LIBRARY_DIR})
    set(Boost_FOUND TRUE)
endif()

# Set libtorch specific variables
if(libtorch_FOUND)
    set(Torch_INCLUDE_DIR ${libtorch_INCLUDE_DIR})
    set(Torch_LIBRARY_DIR ${libtorch_LIBRARY_DIR})
    set(Torch_FOUND TRUE)
endif()

# Print summary
message(STATUS "Third-party library search summary:")
message(STATUS "  oneTBB: ${oneTBB_FOUND}")
message(STATUS "  OpenIGTLink: ${OpenIGTLink_FOUND}")
message(STATUS "  ITK: ${ITK_FOUND}")
message(STATUS "  cpprestsdk: ${cpprestsdk_FOUND}")
message(STATUS "  boost: ${boost_FOUND}")
message(STATUS "  libtorch: ${libtorch_FOUND}")
