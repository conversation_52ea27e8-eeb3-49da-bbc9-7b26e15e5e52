stages:
  - build

build_ubuntu_1604_cuda10_ros:
  stage: build
  tags:
    - linux
    - g++
  image: supra-builder:ros-kinetic-cuda-10.0-ubuntu-16.04
  script:
    - mkdir -p build_gcc
    - cd build_gcc
    - cmake .. -DCMAKE_CXX_COMPILER=g++ -DSUPRA_DEVICE_CEPHASONICS=OFF -DSUPRA_DEVICE_ULTRASONIX=OFF -DSUPRA_DOWNLOAD_SAMPLEDATA=OFF -DSUPRA_USE_CAMPVIS=OFF -DSUPRA_PROFILING=OFF -DSUPRA_INTERFACE_ROS=OFF -DSUPRA_DEVICE_TRACKING_ROS=ON -DSUPRA_WARNINGS_AS_ERRORS=ON
    - make
    
build_ubuntu_1804_cuda10_nohw:
  stage: build
  tags:
    - linux
    - g++
  image: supra-builder:cuda-10.0-ubuntu-18.04
  script:
    - mkdir -p build_gcc
    - cd build_gcc
    - cmake .. -DCMAKE_CXX_COMPILER=g++ -DSUPRA_DEVICE_CEPHASONICS=OFF -DSUPRA_DEVICE_ULTRASONIX=OFF -DSUPRA_DOWNLOAD_SAMPLEDATA=OFF -DSUPRA_USE_CAMPVIS=OFF -DSUPRA_PROFILING=OFF -DSUPRA_INTERFACE_ROS=OFF -DSUPRA_WARNINGS_AS_ERRORS=ON -DSUPRA_TORCH=ON -DTorch_DIR=/opt/libtorch/share/cmake/Torch
    - make
build_ubuntu_1604_cuda10_portable:
  stage: build
  tags:
    - linux
    - g++
  image: supra-builder:cuda-10.0-ubuntu-16.04
  script:
    - mkdir -p build_gcc
    - cd build_gcc
    - cmake .. -DCMAKE_CXX_COMPILER=g++ -DSUPRA_DEVICE_CEPHASONICS=OFF -DSUPRA_DEVICE_ULTRASONIX=OFF -DSUPRA_DOWNLOAD_SAMPLEDATA=OFF -DSUPRA_USE_CAMPVIS=OFF -DSUPRA_PROFILING=OFF -DSUPRA_INTERFACE_ROS=OFF -DSUPRA_WARNINGS_AS_ERRORS=ON -DSUPRA_CUDA_PORTABLE=ON
    - make
    
build_ubuntu_1804_cuda_ros:
  stage: build
  tags:
    - linux
    - g++
  image: supra-builder:ros-melodic-cuda-10.0-ubuntu-18.04
  script:
    - mkdir -p build_gcc
    - cd build_gcc
    - cmake .. -DCMAKE_CXX_COMPILER=g++ -DSUPRA_DEVICE_CEPHASONICS=OFF -DSUPRA_DEVICE_ULTRASONIX=OFF -DSUPRA_DOWNLOAD_SAMPLEDATA=OFF -DSUPRA_USE_CAMPVIS=OFF -DSUPRA_PROFILING=OFF -DSUPRA_INTERFACE_ROS=OFF -DSUPRA_DEVICE_TRACKING_ROS=ON -DSUPRA_WARNINGS_AS_ERRORS=ON
    - make

build_ubuntu_1404_cuda_cephasonics:
  stage: build
  tags:
    - linux
    - g++
  image: supra-builder:ros-indigo-cuda-10.0-ubuntu-14.04-cephasonics
  script:
    - mkdir -p build_gcc
    - cd build_gcc
    - cmake .. -DCMAKE_CXX_COMPILER=g++ -DSUPRA_DEVICE_CEPHASONICS=ON -DSUPRA_DEVICE_ULTRASONIX=OFF -DSUPRA_DOWNLOAD_SAMPLEDATA=OFF -DSUPRA_USE_CAMPVIS=OFF -DSUPRA_PROFILING=OFF -DSUPRA_INTERFACE_ROS=OFF -DSUPRA_DEVICE_TRACKING_ROS=ON -DSUPRA_WARNINGS_AS_ERRORS=ON
    - make    
    
build_ubuntu_nocuda:
  stage: build
  tags:
    - linux
    - g++
  image: supra-builder:nocuda-ubuntu
  script:
    - mkdir -p build_gcc
    - cd build_gcc
    - cmake .. -DCMAKE_CXX_COMPILER=g++ -DSUPRA_BEAMFORMER=OFF -DSUPRA_DEVICE_CEPHASONICS=OFF -DSUPRA_DEVICE_ULTRASONIX=OFF -DSUPRA_DOWNLOAD_SAMPLEDATA=OFF -DSUPRA_USE_CAMPVIS=OFF -DSUPRA_PROFILING=OFF -DSUPRA_INTERFACE_ROS=OFF -DSUPRA_DEVICE_TRACKING_ROS=OFF -DSUPRA_CUDA=OFF -DSUPRA_WARNINGS_AS_ERRORS=ON
    - make    

build_windows_vs2017:
  stage: build
  tags:
    - windows
  script:
    - IF NOT EXIST "build_msvs" mkdir "build_msvs"
    - cd build_msvs
    - cmake .. -G "Visual Studio 15 2017 Win64" -DSUPRA_DEVICE_CEPHASONICS=OFF -DSUPRA_DEVICE_ULTRASONIX=OFF -DSUPRA_DOWNLOAD_SAMPLEDATA=OFF -DSUPRA_USE_CAMPVIS=OFF -DSUPRA_PROFILING=OFF -DSUPRA_INTERFACE_ROS=OFF -DOpenIGTLink_DIR=C:/Users/<USER>/repo/OpenIGTLink/build_vs2017 -DQt5_DIR=C:/Users/<USER>/repo/QT5/5.12.3/msvc2017_64/lib/cmake/Qt5
    - cmake --build .

build_windows_vs2015:
  stage: build
  tags:
    - windows
  script:
    - IF NOT EXIST "build_msvs" mkdir "build_msvs"
    - cd build_msvs
    - cmake .. -G "Visual Studio 14 2015 Win64" -DSUPRA_DEVICE_CEPHASONICS=OFF -DSUPRA_DEVICE_ULTRASONIX=OFF -DSUPRA_DOWNLOAD_SAMPLEDATA=OFF -DSUPRA_USE_CAMPVIS=OFF -DSUPRA_PROFILING=OFF -DSUPRA_INTERFACE_ROS=OFF -DOpenIGTLink_DIR=C:/Users/<USER>/repo/OpenIGTLink/build -DQt5_DIR=C:/Users/<USER>/repo/QT5/5.10.0/msvc2015_64/lib/cmake/Qt5
    - cmake --build .

build_windows_vs2015_win32:
  stage: build
  tags:
    - windows
  script:
    - IF NOT EXIST "build_msvs_win32" mkdir "build_msvs_win32"
    - cd build_msvs_win32
    - cmake .. -G "Visual Studio 14 2015" -DSUPRA_CUDA=OFF -DSUPRA_DEVICE_CEPHASONICS=OFF -DSUPRA_DEVICE_ULTRASONIX=ON -DSUPRA_DOWNLOAD_SAMPLEDATA=OFF -DSUPRA_USE_CAMPVIS=OFF -DSUPRA_PROFILING=OFF -DSUPRA_INTERFACE_ROS=OFF -DSUPRA_EXTERNALS="C:/Users/<USER>/repo" -DOpenIGTLink_DIR=C:/Users/<USER>/repo/OpenIGTLink/build_win32 -DQt5_DIR=C:/Users/<USER>/repo/QT5/5.10.0/msvc2015/lib/cmake/Qt5
    - cmake --build .