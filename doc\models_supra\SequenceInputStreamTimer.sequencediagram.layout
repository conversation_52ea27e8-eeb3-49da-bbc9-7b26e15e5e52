﻿<?xml version="1.0" encoding="utf-8"?>
<sequenceDesignerDiagram dslVersion="1.0.0.0" absoluteBounds="0, 0, 11, 10.25" name="UMLSequenceDiagram1">
  <SequenceDesignerModelMoniker Id="53c67020-3e0f-4fe1-8938-d5f20144dd53" />
  <nestedChildShapes>
    <lifelineShape Id="3d06028a-6242-4b9a-a7c7-805e88b6d15b" absoluteBounds="5.125, 1, 0.15, 9.0380034592002652" visible="true" visualStyleMode="Modified">
      <lifelineMoniker Id="0c0c9e16-4dd2-4987-a87b-0c1da6410356" LastKnownName=": AbstractInput" />
      <relativeChildShapes>
        <lifelineHoverShape Id="6a1f39e7-6c68-4bce-a914-1e305c835f4d" absoluteBounds="5.125, 1, 0, 9">
          <lifelineMoniker Id="0c0c9e16-4dd2-4987-a87b-0c1da6410356" LastKnownName=": AbstractInput" />
        </lifelineHoverShape>
        <umlLifelineHeadShape Id="2e7322da-b777-41f0-a325-56eb2193bd93" absoluteBounds="4.5802083333333332, 0.6, 1.2395833333333335, 0.4" customColor="White" visualStyleMode="Modified">
          <lifelineMoniker Id="0c0c9e16-4dd2-4987-a87b-0c1da6410356" LastKnownName=": AbstractInput" />
          <relativeChildShapes />
        </umlLifelineHeadShape>
        <umlExecutionSpecificationShape Id="735c38d1-d21c-46b1-a61d-f021f2ce2ca4" absoluteBounds="5.125, 1.3, 0.15, 8.4380034592002637" customColor="184, 204, 215" visualStyleMode="Modified">
          <behaviorExecutionSpecificationMoniker Id="29d8a69d-408b-425f-b0c7-468a7a8e40f0" LastKnownName="BehaviorExecutionSpecification6" />
          <relativeChildShapes>
            <umlExecutionSpecificationShape Id="23b41397-3494-4f2b-9b88-4becd3d1570a" absoluteBounds="5.2, 1.85, 0.15, 0.55000000000000027" customColor="184, 204, 215" visualStyleMode="Modified">
              <behaviorExecutionSpecificationMoniker Id="dc93ed25-963d-465f-b21f-8b99b7ee74da" LastKnownName="BehaviorExecutionSpecification7" />
            </umlExecutionSpecificationShape>
            <umlExecutionSpecificationShape Id="bcfb2a9c-278b-411d-9e64-cf374e46a319" absoluteBounds="5.2, 3, 0.15, 1.1499999999999995" customColor="184, 204, 215" visualStyleMode="Modified">
              <behaviorExecutionSpecificationMoniker Id="2c3af24b-78e7-44fe-9969-2b08a7f712db" LastKnownName="BehaviorExecutionSpecification9" />
            </umlExecutionSpecificationShape>
            <umlExecutionSpecificationShape Id="ad7c30e7-a6cb-489e-b727-01b2e2015e0c" absoluteBounds="5.2, 4.4499999999999993, 0.15, 4.6880034592002637" customColor="184, 204, 215" visualStyleMode="Modified">
              <behaviorExecutionSpecificationMoniker Id="f229b59c-2fd4-4f8b-97f8-72ba80bb7c18" LastKnownName="BehaviorExecutionSpecification10" />
              <relativeChildShapes>
                <umlExecutionSpecificationShape Id="881e042d-31ee-4631-81ca-a50fa8b45504" absoluteBounds="5.275, 6.8180034596472963, 0.15, 0.55" customColor="184, 204, 215" visualStyleMode="Modified">
                  <behaviorExecutionSpecificationMoniker Id="bc520769-3fdc-4247-a8c1-aa71fbee1b19" LastKnownName="BehaviorExecutionSpecification17" />
                </umlExecutionSpecificationShape>
              </relativeChildShapes>
            </umlExecutionSpecificationShape>
          </relativeChildShapes>
        </umlExecutionSpecificationShape>
      </relativeChildShapes>
    </lifelineShape>
    <lifelineShape Id="03c70669-dedf-4865-a8cf-4cbd6e84b403" absoluteBounds="7.175, 1, 0.15, 8.7380034592002644" visible="true" visualStyleMode="Modified">
      <lifelineMoniker Id="f3b50bac-a472-4f2c-bee9-c164f3834606" LastKnownName=": InputDevice:AbstractInput" />
      <relativeChildShapes>
        <lifelineHoverShape Id="50d77868-e9ac-4e66-b503-e04944d3d21d" absoluteBounds="7.175, 1, 0, 8.75">
          <lifelineMoniker Id="f3b50bac-a472-4f2c-bee9-c164f3834606" LastKnownName=": InputDevice:AbstractInput" />
        </lifelineHoverShape>
        <umlLifelineHeadShape Id="fd428a76-8d89-4681-86ae-6a93d0e3c7ff" absoluteBounds="6.3125, 0.6, 1.875, 0.4" customColor="White" visualStyleMode="Modified">
          <lifelineMoniker Id="f3b50bac-a472-4f2c-bee9-c164f3834606" LastKnownName=": InputDevice:AbstractInput" />
          <relativeChildShapes />
        </umlLifelineHeadShape>
        <umlExecutionSpecificationShape Id="adfd782f-5d3c-49e9-a8b2-642df9bc305a" absoluteBounds="7.175, 2.7, 0.15, 6.7380034592002636" customColor="184, 204, 215" visualStyleMode="Modified">
          <behaviorExecutionSpecificationMoniker Id="049ef1b3-5888-4fd6-8834-eb0892198318" LastKnownName="BehaviorExecutionSpecification8" />
          <relativeChildShapes>
            <umlExecutionSpecificationShape Id="0d28d146-5016-44f0-b640-f2b09c13c311" absoluteBounds="7.1, 5.4180034596472968, 0.15, 2.2499999999999991" customColor="184, 204, 215" visualStyleMode="Modified">
              <behaviorExecutionSpecificationMoniker Id="87b244e3-4534-46f7-9fae-beb2983adf6b" LastKnownName="BehaviorExecutionSpecification11" />
              <relativeChildShapes>
                <umlExecutionSpecificationShape Id="727e8da3-da8d-4617-a1b3-3b4a5ed8deb0" absoluteBounds="7.0249999999999995, 5.9680034596472966, 0.15, 0.55" customColor="184, 204, 215" visualStyleMode="Modified">
                  <behaviorExecutionSpecificationMoniker Id="8e15255e-8fed-4e7a-8f29-13634ecc06af" LastKnownName="BehaviorExecutionSpecification16" />
                </umlExecutionSpecificationShape>
              </relativeChildShapes>
            </umlExecutionSpecificationShape>
          </relativeChildShapes>
        </umlExecutionSpecificationShape>
      </relativeChildShapes>
    </lifelineShape>
    <lifelineShape Id="65ba9ab3-938b-4775-bcc1-839deb109e29" absoluteBounds="1.025, 1, 0.15, 7" visible="true" visualStyleMode="Modified">
      <lifelineMoniker Id="adeace2b-d80e-4c48-b728-12535adc5c8b" LastKnownName="Controller" />
      <relativeChildShapes>
        <umlLifelineHeadShape Id="6b6aace4-3dd2-42de-ae23-bb3ef0c20b8a" absoluteBounds="0.59999999999999987, 0.6, 1, 0.4" customColor="White" visualStyleMode="Modified">
          <lifelineMoniker Id="adeace2b-d80e-4c48-b728-12535adc5c8b" LastKnownName="Controller" />
          <relativeChildShapes />
        </umlLifelineHeadShape>
        <lifelineHoverShape Id="2d8a59be-fdc5-4523-95f7-7eaa465f3a40" absoluteBounds="1.025, 1, 0, 7">
          <lifelineMoniker Id="adeace2b-d80e-4c48-b728-12535adc5c8b" LastKnownName="Controller" />
        </lifelineHoverShape>
      </relativeChildShapes>
    </lifelineShape>
    <asyncMessageConnector edgePoints="[(1.1 : 1.3); (5.125 : 1.3)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <lifelineShapeMoniker Id="65ba9ab3-938b-4775-bcc1-839deb109e29" />
        <umlExecutionSpecificationShapeMoniker Id="735c38d1-d21c-46b1-a61d-f021f2ce2ca4" />
      </nodes>
    </asyncMessageConnector>
    <syncSelfMessageConnector edgePoints="[(5.275 : 1.6); (5.525 : 1.6); (5.525 : 1.85); (5.35 : 1.85)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="735c38d1-d21c-46b1-a61d-f021f2ce2ca4" />
        <umlExecutionSpecificationShapeMoniker Id="23b41397-3494-4f2b-9b88-4becd3d1570a" />
      </nodes>
    </syncSelfMessageConnector>
    <syncMessageConnector edgePoints="[(5.275 : 2.7); (7.175 : 2.7)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="735c38d1-d21c-46b1-a61d-f021f2ce2ca4" />
        <umlExecutionSpecificationShapeMoniker Id="adfd782f-5d3c-49e9-a8b2-642df9bc305a" />
      </nodes>
    </syncMessageConnector>
    <returnMessageConnector edgePoints="[(7.175 : 9.43800345920026); (5.275 : 9.43800345920026)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="adfd782f-5d3c-49e9-a8b2-642df9bc305a" />
        <umlExecutionSpecificationShapeMoniker Id="735c38d1-d21c-46b1-a61d-f021f2ce2ca4" />
      </nodes>
    </returnMessageConnector>
    <syncMessageConnector edgePoints="[(7.175 : 3); (5.35 : 3)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="adfd782f-5d3c-49e9-a8b2-642df9bc305a" />
        <umlExecutionSpecificationShapeMoniker Id="bcfb2a9c-278b-411d-9e64-cf374e46a319" />
      </nodes>
    </syncMessageConnector>
    <returnMessageConnector edgePoints="[(5.35 : 4.15); (7.175 : 4.15)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="bcfb2a9c-278b-411d-9e64-cf374e46a319" />
        <umlExecutionSpecificationShapeMoniker Id="adfd782f-5d3c-49e9-a8b2-642df9bc305a" />
      </nodes>
    </returnMessageConnector>
    <syncMessageConnector edgePoints="[(7.175 : 4.45); (5.35 : 4.45)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="adfd782f-5d3c-49e9-a8b2-642df9bc305a" />
        <umlExecutionSpecificationShapeMoniker Id="ad7c30e7-a6cb-489e-b727-01b2e2015e0c" />
      </nodes>
    </syncMessageConnector>
    <returnMessageConnector edgePoints="[(5.35 : 9.13800345920026); (7.175 : 9.13800345920026)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="ad7c30e7-a6cb-489e-b727-01b2e2015e0c" />
        <umlExecutionSpecificationShapeMoniker Id="adfd782f-5d3c-49e9-a8b2-642df9bc305a" />
      </nodes>
    </returnMessageConnector>
    <lifelineShape Id="6943d5f3-ff93-4331-85c0-b06ca7419793" absoluteBounds="3.075, 1, 0.15, 8.138003459200263" visible="true" visualStyleMode="Modified">
      <lifelineMoniker Id="23565e29-1b7b-44d0-ad58-035326ca2895" LastKnownName="SingleThreadTimer" />
      <relativeChildShapes>
        <umlLifelineHeadShape Id="d39b426d-3606-40b2-9c18-e1aa29d07a2c" absoluteBounds="2.4826447200775146, 0.6, 1.3347105598449707, 0.4" customColor="White" visualStyleMode="Modified">
          <lifelineMoniker Id="23565e29-1b7b-44d0-ad58-035326ca2895" LastKnownName="SingleThreadTimer" />
          <relativeChildShapes />
        </umlLifelineHeadShape>
        <lifelineHoverShape Id="96fa246c-24ee-4740-ad6a-5a354ecc281f" absoluteBounds="3.075, 1, 0, 8.125">
          <lifelineMoniker Id="23565e29-1b7b-44d0-ad58-035326ca2895" LastKnownName="SingleThreadTimer" />
        </lifelineHoverShape>
        <umlExecutionSpecificationShape Id="f53648e0-4112-4d85-b72a-56e79711853a" absoluteBounds="3.075, 3.3, 0.15, 0.55" customColor="184, 204, 215" visualStyleMode="Modified">
          <behaviorExecutionSpecificationMoniker Id="e668acc2-4dd0-425b-a7f7-561b11fc8046" LastKnownName="BehaviorExecutionSpecification13" />
        </umlExecutionSpecificationShape>
        <umlExecutionSpecificationShape Id="0c638194-9a30-4ab3-9ecf-d98659ec9056" absoluteBounds="3.075, 7.9680034596472957, 0.15, 0.55000000000000071" customColor="184, 204, 215" visualStyleMode="Modified">
          <behaviorExecutionSpecificationMoniker Id="dcb1930c-4ebe-4245-815e-1f752655ac8c" LastKnownName="BehaviorExecutionSpecification12" />
        </umlExecutionSpecificationShape>
      </relativeChildShapes>
    </lifelineShape>
    <syncMessageConnector edgePoints="[(5.35 : 5.4180034596473); (7.1 : 5.41800345964729)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="ad7c30e7-a6cb-489e-b727-01b2e2015e0c" />
        <umlExecutionSpecificationShapeMoniker Id="0d28d146-5016-44f0-b640-f2b09c13c311" />
      </nodes>
    </syncMessageConnector>
    <returnMessageConnector edgePoints="[(7.1 : 7.6680034596473); (5.35 : 7.6680034596473)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="0d28d146-5016-44f0-b640-f2b09c13c311" />
        <umlExecutionSpecificationShapeMoniker Id="ad7c30e7-a6cb-489e-b727-01b2e2015e0c" />
      </nodes>
    </returnMessageConnector>
    <syncMessageConnector edgePoints="[(5.2 : 7.9680034596473); (3.225 : 7.96800345964729)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="ad7c30e7-a6cb-489e-b727-01b2e2015e0c" />
        <umlExecutionSpecificationShapeMoniker Id="0c638194-9a30-4ab3-9ecf-d98659ec9056" />
      </nodes>
    </syncMessageConnector>
    <returnMessageConnector edgePoints="[(3.225 : 8.51800345964729); (5.2 : 8.5180034596473)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="0c638194-9a30-4ab3-9ecf-d98659ec9056" />
        <umlExecutionSpecificationShapeMoniker Id="ad7c30e7-a6cb-489e-b727-01b2e2015e0c" />
      </nodes>
    </returnMessageConnector>
    <combinedFragmentShape Id="7afbf7d4-bf6d-4009-a682-2ab33c843e1b" absoluteBounds="2.555000000447035, 4.7499999999999991, 5.0900000110268593, 4.0880034592002632" visible="true" visualStyleMode="Modified">
      <combinedFragmentMoniker Id="0e09c508-b348-449b-a459-d243edafe5c6" LastKnownName="CombinedFragment1" />
      <nestedChildShapes>
        <interactionOperandShape Id="51fd5fc3-efcd-449b-8bef-8c1356c3e4de" absoluteBounds="2.575, 4.9999999999999991, 5.0450000120326877, 3.818003459647298">
          <interactionOperandMoniker Id="17504b71-3ccd-4a99-9b88-84b741cfbe1c" LastKnownName="InteractionOperand1" />
        </interactionOperandShape>
      </nestedChildShapes>
    </combinedFragmentShape>
    <syncMessageConnector edgePoints="[(5.2 : 3.3); (3.225 : 3.3)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="bcfb2a9c-278b-411d-9e64-cf374e46a319" />
        <umlExecutionSpecificationShapeMoniker Id="f53648e0-4112-4d85-b72a-56e79711853a" />
      </nodes>
    </syncMessageConnector>
    <returnMessageConnector edgePoints="[(3.225 : 3.85); (5.2 : 3.85)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="f53648e0-4112-4d85-b72a-56e79711853a" />
        <umlExecutionSpecificationShapeMoniker Id="bcfb2a9c-278b-411d-9e64-cf374e46a319" />
      </nodes>
    </returnMessageConnector>
    <syncSelfMessageConnector edgePoints="[(7.1 : 5.7180034596473); (6.85 : 5.7180034596473); (6.85 : 5.9680034596473); (7.025 : 5.9680034596473)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="0d28d146-5016-44f0-b640-f2b09c13c311" />
        <umlExecutionSpecificationShapeMoniker Id="727e8da3-da8d-4617-a1b3-3b4a5ed8deb0" />
      </nodes>
    </syncSelfMessageConnector>
    <syncMessageConnector edgePoints="[(7.1 : 6.8180034596473); (5.425 : 6.8180034596473)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="0d28d146-5016-44f0-b640-f2b09c13c311" />
        <umlExecutionSpecificationShapeMoniker Id="881e042d-31ee-4631-81ca-a50fa8b45504" />
      </nodes>
    </syncMessageConnector>
    <returnMessageConnector edgePoints="[(5.425 : 7.3680034596473); (7.1 : 7.3680034596473)]" fixedFrom="Caller" fixedTo="Caller" TargetRelationshipDomainClassId="e24617ce-6c7e-4c7d-802a-63014f02e313" customColor="Black" visible="true" visualStyleMode="Modified" messageId="00000000-0000-0000-0000-000000000000">
      <relativeChildShapes />
      <nodes>
        <umlExecutionSpecificationShapeMoniker Id="881e042d-31ee-4631-81ca-a50fa8b45504" />
        <umlExecutionSpecificationShapeMoniker Id="0d28d146-5016-44f0-b640-f2b09c13c311" />
      </nodes>
    </returnMessageConnector>
  </nestedChildShapes>
</sequenceDesignerDiagram>